# IELTS Exams Platform Overview

## Project Purpose
Full-stack IELTS examination platform với Next.js frontend và NestJS backend để xác thực và quản lý user.

## Tech Stack

### Frontend (fe-ielts-exams)
- **Framework**: Next.js 15 với App Router & React 19
- **Styling**: TailwindCSS 4 + Radix UI components
- **Forms**: React Hook Form + Zod validation
- **HTTP**: Axios với interceptors
- **Testing**: Playwright E2E tests
- **Port**: 3000

### Backend (be-ielts-exams)
- **Framework**: NestJS 10 với TypeScript
- **Database**: Prisma ORM + MySQL
- **Authentication**: JWT (RSA signed) + httpOnly cookies
- **Security**: Helmet, CORS, rate limiting, input sanitization
- **Testing**: Jest unit & E2E tests
- **Port**: 8228

## Project Structure
```
ielts-exams/
├── fe-ielts-exams/     # Next.js frontend
│   ├── src/app/        # App Router pages
│   ├── components/     # UI components
│   ├── contexts/       # React contexts
│   ├── services/       # API services
│   └── tests/          # Playwright E2E tests
└── be-ielts-exams/     # NestJS backend API
    ├── src/modules/    # Feature modules
    ├── keys/           # JWT RSA keys
    └── prisma/         # Database schema
```
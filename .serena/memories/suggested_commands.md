# Suggested Development Commands

## Server Startup Commands

### Frontend (port 3000)
```bash
cd fe-ielts-exams
npm run dev          # Development với Turbopack
npm run build        # Build production
npm start           # Start production server
npm run lint        # Lint code
```

### Backend (port 8228)
```bash
cd be-ielts-exams
yarn start:dev      # Development watch mode
yarn build          # Build production
yarn start:prod     # Start production server
yarn lint           # Lint code
yarn format         # Format code
```

## Testing Commands

### Frontend Testing
```bash
cd fe-ielts-exams
npx playwright test                           # Run all E2E tests
npx playwright test tests/auth/auth.e2e.spec.ts  # Run specific test
npx playwright test --headed                  # Run with visible browser
npx playwright test --debug                   # Debug mode
npx playwright show-report                    # View test reports
```

### Backend Testing
```bash
cd be-ielts-exams
yarn test           # All unit tests
yarn test:watch     # Watch mode
yarn test:cov       # Test coverage
yarn test:e2e       # E2E tests
```

## Database Commands
```bash
cd be-ielts-exams
npx prisma generate    # Generate Prisma client
npx prisma db push     # Push schema to DB
npx prisma migrate dev # Create migration
npx prisma studio      # Open Prisma Studio
```

## Security Setup (First Time)
```bash
cd be-ielts-exams
./generate-keys.sh     # Generate JWT RSA keys
cp .env.example .env   # Setup environment
```

## System Commands (macOS)
```bash
find . -name "*.ts" -type f    # Find TypeScript files
grep -r "pattern" src/         # Search in source code
ls -la                         # List files with details
cd path/to/directory          # Change directory
```
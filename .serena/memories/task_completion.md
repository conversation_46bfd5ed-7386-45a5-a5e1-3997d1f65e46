# Task Completion Guidelines

## After Coding Tasks
1. **Lint và Format**: Chạy linting và formatting tools
   - Frontend: `npm run lint`
   - Backend: `yarn lint && yarn format`

2. **Test Verification**: 
   - Chạy tests liên quan đến code changes
   - Frontend: `npx playwright test` cho E2E tests
   - Backend: `yarn test` cho unit tests

3. **Build Verification**:
   - Frontend: `npm run build` để verify build
   - Backend: `yarn build` để verify compilation

4. **Server Testing**: 
   - Start cả 2 servers và test thực tế
   - Frontend: `npm run dev` (port 3000)
   - Backend: `yarn start:dev` (port 8228)

5. **Security Check**:
   - Verify JWT keys tồn tại trong `be-ielts-exams/keys/`
   - Check environment variables được setup đúng

## Special Notes
- Luôn test với browser thực để verify user experience
- Sử dụng Playwright MCP để test authentication flows
- Keep source files under 500 lines
- Response bằng tiếng Việt trong chat
# Build Admin Task - IELTS Quiz Management System

## Tổng Quan Hệ Thống

Hệ thống IELTS Quiz được xây dựng với kiến trúc multi-part quiz, hỗ trợ cả Reading và Listening tests. Mỗi quiz bao gồm nhiều parts, mỗi part có content riêng và các câu hỏi với nhiều định dạng khác nhau.

## Cấu Trúc Dữ Liệu

### 1. MultiPartQuiz Interface
```typescript
interface MultiPartQuiz {
  title: string
  totalTimeLimit: number // Tổng thời gian (phút)
  testType: 'reading' | 'listening'
  parts: QuizPart[]
  metadata: {
    totalQuestions: number // Tổng số câu hỏi (expanded count)
  }
}
```

### 2. QuizPart Interface
```typescript
interface QuizPart {
  partNumber: number
  title: string
  content: {
    title: string
    subtitle?: string
    paragraphs?: Paragraph[] // Cho Reading
    audioUrl?: string // Cho Listening
  }
  questions: Question[]
  sharedDragOptions?: DragOption[] // Cho DRAG_AND_DROP questions
}
```

### 3. Question Interface
```typescript
interface Question {
  id: string
  type: QuestionType
  prompt?: string // Câu hỏi chính
  text?: string // Nội dung câu hỏi
  instruction?: string // Hướng dẫn làm bài
  options?: QuestionOption[] // Cho multiple choice
  correctAnswer?: string // Đáp án đúng
  paragraphLabels?: string[] // Cho paragraph matching
  tableData?: TableData // Cho table completion/matching
  maxSelections?: number // Cho multiple select
  correctAnswers?: string[] // Cho multiple correct answers
}
```

## Question Types và Cách Render

### 1. MULTIPLE_CHOICE
**Đặc điểm:**
- Một câu hỏi với nhiều lựa chọn (A, B, C, D)
- Chỉ chọn được 1 đáp án
- Có `prompt` và `options`

**Cấu trúc dữ liệu:**
```json
{
  "id": "p1q1",
  "type": "MULTIPLE_CHOICE",
  "prompt": "What convinced Khudi that the corpse was a baby mammoth?",
  "options": [
    {"id": "a", "text": "Its perfect preservation"},
    {"id": "b", "text": "His previous experience with mammoth tusks"},
    {"id": "c", "text": "The location where it was found"},
    {"id": "d", "text": "Its diminutive size"}
  ]
}
```

**Logic render:**
- Radio buttons cho từng option
- Lưu answer là option.id
- Hiển thị prompt làm tiêu đề

### 2. TRUE_FALSE_NOTGIVEN
**Đặc điểm:**
- Statement với 3 lựa chọn cố định: TRUE, FALSE, NOT GIVEN
- Có `text` chứa statement và `instruction`

**Cấu trúc dữ liệu:**
```json
{
  "id": "p1q2",
  "type": "TRUE_FALSE_NOTGIVEN",
  "text": "The mammoth extinction occurred at the same time as climate change.",
  "correctAnswer": "TRUE",
  "instruction": "Write TRUE if the statement agrees with the information..."
}
```

**Logic render:**
- 3 radio buttons cố định: TRUE, FALSE, NOT GIVEN
- Hiển thị statement và instruction

### 3. SENTENCE_COMPLETION
**Đặc điểm:**
- Câu có chỗ trống cần điền
- Input field được đặt trong câu
- Có giới hạn từ (thường "NO MORE THAN TWO WORDS")

**Cấu trúc dữ liệu:**
```json
{
  "id": "p1q3",
  "type": "SENTENCE_COMPLETION",
  "text": "The baby mammoth was named Lyuba by the researchers.",
  "correctAnswer": "Lyuba",
  "instruction": "Choose NO MORE THAN TWO WORDS from the passage for each answer."
}
```

**Logic render:**
- Tách câu dựa trên correctAnswer để tìm vị trí input
- Đặt input field tại vị trí thích hợp
- Hiển thị instruction

### 4. PARAGRAPH_MATCHING_TABLE
**Đặc điểm:**
- Ghép thông tin với paragraph (A, B, C...)
- Có `paragraphLabels` để định nghĩa các lựa chọn

**Cấu trúc dữ liệu:**
```json
{
  "id": "p3q1",
  "type": "PARAGRAPH_MATCHING_TABLE",
  "text": "a comparison between current technological change and historical precedent",
  "correctAnswer": "B",
  "instruction": "Choose the correct letter A-C for each answer.",
  "paragraphLabels": ["A", "B", "C"]
}
```

**Logic render:**
- Dropdown/select với các paragraph labels
- Hiển thị text description
- Lưu answer là paragraph label

### 5. DRAG_AND_DROP
**Đặc điểm:**
- Kéo thả options vào các câu hỏi
- `sharedDragOptions` được định nghĩa ở part level
- Multiple questions share cùng một pool options

**Cấu trúc dữ liệu:**
```json
// Ở part level:
"sharedDragOptions": [
  {"id": "opt_a", "label": "A", "text": "can be found in unusual thoughts and chance events."},
  {"id": "opt_b", "label": "B", "text": "can be taught in business schools."}
],
"questions": [
  {
    "id": "p4q1",
    "type": "DRAG_AND_DROP",
    "text": "The usual business environment",
    "correctAnswer": "opt_a",
    "instruction": "Complete each sentence with the correct ending, A-H, below."
  }
]
```

**Logic render:**
- Available options pool (chưa được sử dụng)
- Drop zones cho từng question
- Drag and drop functionality
- Continuous numbering

### 6. TABLE_COMPLETION
**Đặc điểm:**
- Hoàn thành bảng với input fields
- `tableData` chứa headers, rows, và answers mapping
- Multiple inputs per question

**Cấu trúc dữ liệu:**
```json
{
  "id": "l1q1",
  "type": "TABLE_COMPLETION",
  "text": "Complete the table below. Write ONE WORD AND/OR A NUMBER for each answer.",
  "instruction": "BEECHEN FESTIVAL",
  "tableData": {
    "headers": ["Date", "Time", "Activity", "Place", "Comments"],
    "rows": [
      {
        "cells": ["June 20th", "2:30 pm", "tour", "meet at the station", "includes a visit to an old flour mill"],
        "answers": { "2": "30", "3": "station" }
      }
    ]
  }
}
```

**Logic render:**
- Render bảng với headers
- Input fields tại vị trí có answers mapping
- Question numbering cho từng input

### 7. MULTIPLE_SELECT
**Đặc điểm:**
- Chọn nhiều đáp án (checkbox)
- Có `maxSelections` để giới hạn số lựa chọn
- Instruction chỉ rõ số lượng cần chọn

**Cấu trúc dữ liệu:**
```json
{
  "id": "l3q18",
  "type": "MULTIPLE_SELECT",
  "prompt": "Which THREE of the following features of the area in Spain does the speaker talk about?",
  "instruction": "Choose THREE correct answers.",
  "options": [
    {"id": "a", "text": "altitude"},
    {"id": "b", "text": "coastline"},
    {"id": "c", "text": "economy"}
  ],
  "maxSelections": 3
}
```

**Logic render:**
- Checkbox cho từng option
- Validation số lượng selections
- Lưu answers dạng comma-separated string

### 8. MATCHING_TABLE
**Đặc điểm:**
- Bảng ghép cặp với options A, B, C
- Multiple rows với cùng options
- `tableData` chứa rows và options mapping

**Cấu trúc dữ liệu:**
```json
{
  "id": "l4q25",
  "type": "MATCHING_TABLE",
  "prompt": "How did the following categories of student markers compare with the rest of the group when marking student presentations?",
  "instruction": "Choose the correct letter, A, B or C, next to questions. NB You may use any letter more than once.",
  "tableData": {
    "headers": ["Categories of student markers", "A", "B", "C"],
    "options": {
      "A": "They gave higher marks.",
      "B": "They gave lower marks.",
      "C": "Their marks were not significantly different."
    },
    "rows": [
      {
        "label": "male students marking female presenters",
        "questionId": "l4q25",
        "correctAnswer": "B"
      }
    ]
  }
}
```

**Logic render:**
- Bảng với headers
- Dropdown/radio cho từng row
- Shared options cho tất cả rows

## State Management

### 1. MultiPartQuizState
```typescript
interface MultiPartQuizState {
  currentPart: number
  answers: Record<string, string> // questionId -> answer
  partProgress: Record<number, PartProgress>
  timeRemaining: Record<number, number>
  overallTimeRemaining: number
  isSubmitted: boolean
}
```

### 2. Answer Storage Format
- **Single answer:** `questionId: "answer_value"`
- **Multiple select:** `questionId: "option1,option2,option3"`
- **Table completion:** `questionId_answerKey: "value"` (e.g., `l1q1_2: "30"`)
- **Drag and drop:** `questionId: "option_id"`

## Utility Functions

### 1. Question Numbering
- **Continuous numbering:** Questions được đánh số liên tục qua các parts
- **Expanded count:** Một số question types đếm như nhiều questions
  - TABLE_COMPLETION: số lượng inputs trong bảng
  - MULTIPLE_SELECT: maxSelections
  - MATCHING_TABLE: số lượng rows
  - Others: 1

### 2. Progress Tracking
- Track answered questions per part
- Calculate completion percentage
- Validate required fields

### 3. Storage Management
- localStorage để persist quiz data
- Separate storage cho reading và listening
- Initialize và update functions

## Dataflow

### 1. Quiz Initialization
1. Load JSON data từ files
2. Initialize localStorage
3. Create MultiPartQuizState
4. Set up timers

### 2. Answer Handling
1. User input → handleAnswerChange
2. Update quizState.answers
3. Trigger re-render
4. Update progress tracking

### 3. Navigation
1. Part navigation → handlePartChange
2. Question navigation → scroll to question
3. Update currentPart state

### 4. Submission
1. Collect all answers
2. Validate completion
3. Calculate score (if needed)
4. Submit to backend

## Component Architecture

### 1. Main Components
- **QuizHeader:** Timer, progress, navigation
- **QuizFooter:** Part navigation, question overview
- **QuizContentWithSelection:** Content area với text selection
- **ResizablePanel:** Split view cho reading

### 2. Question Components
- **DragDropQuestion:** Dedicated component cho drag/drop
- **Individual renderers:** Trong main page components

### 3. UI Components
- Standard shadcn/ui components
- Custom styling cho quiz-specific elements

## Admin Page Requirements

### 1. Quiz Management
- **CRUD operations** cho quizzes
- **Part management:** Add/edit/delete parts
- **Question management:** Add/edit/delete questions với tất cả types
- **Content management:** Text content, audio files

### 2. Question Type Support
- **Form builders** cho từng question type
- **Validation rules** cho từng type
- **Preview functionality** để test questions

### 3. Data Validation
- **Schema validation** cho quiz structure
- **Answer validation** cho correctAnswers
- **Numbering consistency** checks

### 4. Import/Export
- **JSON import/export** cho bulk operations
- **Template generation** cho new quizzes
- **Backup/restore** functionality

### 5. Analytics
- **Usage statistics** cho quizzes
- **Performance metrics** cho questions
- **Completion rates** tracking

## Technical Considerations

### 1. Database Schema
- Flexible schema để support tất cả question types
- JSON fields cho complex data (tableData, options)
- Proper indexing cho performance

### 2. API Design
- RESTful endpoints cho CRUD operations
- Validation middleware
- File upload handling cho audio

### 3. UI/UX
- Form builders với conditional fields
- Real-time preview
- Drag-and-drop interface cho question ordering

### 4. Performance
- Lazy loading cho large quizzes
- Caching strategies
- Optimized rendering cho complex questions

## Migration Strategy

### 1. Current State
- Static JSON files
- localStorage-based storage
- Client-side only logic

### 2. Target State
- Database-backed storage
- Server-side validation
- Admin interface cho management

### 3. Migration Steps
1. Create database schema
2. Build API endpoints
3. Develop admin interface
4. Migrate existing data
5. Update client-side logic

## Security Considerations

### 1. Data Protection
- Input sanitization
- XSS prevention
- CSRF protection

### 2. Access Control
- Admin authentication
- Role-based permissions
- Audit logging

### 3. Content Security
- File upload validation
- Content filtering
- Backup strategies

## Testing Strategy

### 1. Unit Tests
- Question type renderers
- Utility functions
- State management

### 2. Integration Tests
- Complete quiz flows
- Answer submission
- Navigation logic

### 3. E2E Tests
- Full user journeys
- Admin workflows
- Cross-browser compatibility

---

*Tài liệu này cung cấp foundation hoàn chỉnh cho việc phát triển admin page, bao gồm tất cả question types, logic render, và dataflow hiện tại của hệ thống.*
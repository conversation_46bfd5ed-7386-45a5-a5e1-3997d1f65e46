export { DragDropQuestion } from './drag-drop-question'
export { default as HighlightManagementPopup } from './highlight-management-popup'
export { CompactPartSelector, default as PartNavigation, PartNavigationItem } from './part-navigation'
export { default as QuizCard } from './quiz-card'
export { default as QuizContentWithSelection } from './quiz-content-with-selection'
export { default as QuizFooter } from './quiz-footer'
export { default as QuizHeader } from './quiz-header'
export { default as TextSelectionPopup } from './text-selection-popup'
export { useQuizState } from './use-quiz-state'

// Multi-part quiz utilities
export * from '@/lib/multi-part-quiz-utils'
export * from '@/types/multi-part-quiz'


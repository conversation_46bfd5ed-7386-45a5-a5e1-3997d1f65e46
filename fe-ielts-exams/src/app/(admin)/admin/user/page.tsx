'use client'

import { useEffect, useState } from 'react'
import { useAdminRouteProtection } from '@/hooks/useRouteProtection'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Users, Plus, Trash2, Search, Settings, Eye } from 'lucide-react'
import { toast } from 'react-hot-toast'

interface User {
  id: string
  username: string
  email: string
  name: string
  role: 'USER' | 'ADMIN'
  active: boolean
  createdAt: string
  updatedAt: string
}

interface Quiz {
  id: string
  title: string
  testType: string
  isPublished: boolean
  isPublic: boolean
}

interface UserQuizAccess {
  id: string
  userId: string
  quizId: string
  grantedAt: string
  expiresAt: string | null
  isActive: boolean
  quiz: Quiz
}

export default function UserManagementPage() {
  const { isChecking, isAdmin } = useAdminRouteProtection()
  const [users, setUsers] = useState<User[]>([])
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [userQuizAccess, setUserQuizAccess] = useState<UserQuizAccess[]>([])
  const [showAccessDialog, setShowAccessDialog] = useState(false)
  const [selectedQuizId, setSelectedQuizId] = useState('')
  const [expiresAt, setExpiresAt] = useState('')

  useEffect(() => {
    if (isAdmin && !isChecking) {
      loadUsers()
      loadQuizzes()
    }
  }, [isAdmin, isChecking])

  // Show loading while checking admin access
  if (isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  // If not admin, return null (will be handled by useAdminRouteProtection)
  if (!isAdmin) {
    return null
  }

  const loadUsers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      const data = await response.json()
      if (data.success) {
        setUsers(data.data || [])
      } else {
        toast.error('Không thể tải danh sách người dùng')
      }
    } catch (error) {
      console.error('Failed to load users:', error)
      toast.error('Lỗi khi tải danh sách người dùng')
    } finally {
      setLoading(false)
    }
  }

  const loadQuizzes = async () => {
    try {
      const response = await fetch('/api/quiz?limit=100')
      const data = await response.json()
      if (data.status === 'success') {
        const quizzesData = data?.data?.data || data?.data || []
        setQuizzes(Array.isArray(quizzesData) ? quizzesData : [])
      }
    } catch (error) {
      console.error('Failed to load quizzes:', error)
    }
  }

  const loadUserQuizAccess = async (userId: string) => {
    try {
      const response = await fetch(`/api/admin/users/${userId}/quiz-access`)
      const data = await response.json()
      if (data.success) {
        setUserQuizAccess(data.data || [])
      } else {
        toast.error('Không thể tải quyền truy cập quiz')
      }
    } catch (error) {
      console.error('Failed to load user quiz access:', error)
      toast.error('Lỗi khi tải quyền truy cập quiz')
    }
  }

  const grantQuizAccess = async () => {
    if (!selectedUser || !selectedQuizId) return

    try {
      const response = await fetch(`/api/admin/users/${selectedUser.id}/quiz-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quizId: selectedQuizId,
          expiresAt: expiresAt || null,
        }),
      })

      const data = await response.json()
      if (data.success) {
        toast.success('Đã cấp quyền truy cập quiz thành công')
        loadUserQuizAccess(selectedUser.id)
        setSelectedQuizId('')
        setExpiresAt('')
        setShowAccessDialog(false)
      } else {
        toast.error(data.message || 'Không thể cấp quyền truy cập')
      }
    } catch (error) {
      console.error('Failed to grant quiz access:', error)
      toast.error('Lỗi khi cấp quyền truy cập')
    }
  }

  const revokeQuizAccess = async (accessId: string, quizId: string) => {
    if (!selectedUser) return

    try {
      const response = await fetch(`/api/admin/users/${selectedUser.id}/quiz-access/${accessId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ quizId }),
      })

      const data = await response.json()
      if (data.success) {
        toast.success('Đã thu hồi quyền truy cập thành công')
        loadUserQuizAccess(selectedUser.id)
      } else {
        toast.error(data.message || 'Không thể thu hồi quyền truy cập')
      }
    } catch (error) {
      console.error('Failed to revoke quiz access:', error)
      toast.error('Lỗi khi thu hồi quyền truy cập')
    }
  }

  const filteredUsers = users.filter(user =>
    user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.username.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleUserClick = (user: User) => {
    setSelectedUser(user)
    loadUserQuizAccess(user.id)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-800 flex items-center gap-2">
            <Users className="h-8 w-8" />
            User Management
          </h1>
          <p className="text-gray-600 mt-2">Quản lý người dùng và quyền truy cập quiz</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Users List */}
        <Card>
          <CardHeader>
            <CardTitle>Danh sách người dùng</CardTitle>
            <CardDescription>
              Tổng cộng {users.length} người dùng
            </CardDescription>
            <div className="flex items-center space-x-2">
              <Search className="h-4 w-4 text-gray-400" />
              <Input
                placeholder="Tìm kiếm người dùng..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-sm"
              />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedUser?.id === user.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleUserClick(user)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{user.name}</h3>
                      <p className="text-sm text-gray-600">{user.email}</p>
                      <p className="text-xs text-gray-500">@{user.username}</p>
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <Badge variant={user.role === 'ADMIN' ? 'destructive' : 'default'}>
                        {user.role}
                      </Badge>
                      <Badge variant={user.active ? 'default' : 'secondary'}>
                        {user.active ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* User Quiz Access */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Quyền truy cập Quiz</span>
              {selectedUser && (
                <Dialog open={showAccessDialog} onOpenChange={setShowAccessDialog}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Cấp quyền
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Cấp quyền truy cập Quiz</DialogTitle>
                      <DialogDescription>
                        Cấp quyền cho {selectedUser.name} truy cập quiz
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="quiz">Chọn Quiz</Label>
                        <Select value={selectedQuizId} onValueChange={setSelectedQuizId}>
                          <SelectTrigger>
                            <SelectValue placeholder="Chọn quiz..." />
                          </SelectTrigger>
                          <SelectContent>
                            {quizzes.map((quiz) => (
                              <SelectItem key={quiz.id} value={quiz.id}>
                                {quiz.title} ({quiz.testType})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="expires">Ngày hết hạn (tùy chọn)</Label>
                        <Input
                          id="expires"
                          type="datetime-local"
                          value={expiresAt}
                          onChange={(e) => setExpiresAt(e.target.value)}
                        />
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline" onClick={() => setShowAccessDialog(false)}>
                          Hủy
                        </Button>
                        <Button onClick={grantQuizAccess} disabled={!selectedQuizId}>
                          Cấp quyền
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </CardTitle>
            <CardDescription>
              {selectedUser 
                ? `Quyền truy cập của ${selectedUser.name}`
                : 'Chọn người dùng để xem quyền truy cập'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            {selectedUser ? (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {userQuizAccess.length > 0 ? (
                  userQuizAccess.map((access) => (
                    <div key={access.id} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{access.quiz.title}</h4>
                          <p className="text-sm text-gray-600">
                            {access.quiz.testType} • {access.quiz.isPublic ? 'Public' : 'Private'}
                          </p>
                          <p className="text-xs text-gray-500">
                            Cấp: {new Date(access.grantedAt).toLocaleDateString('vi-VN')}
                            {access.expiresAt && (
                              <> • Hết hạn: {new Date(access.expiresAt).toLocaleDateString('vi-VN')}</>
                            )}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={access.isActive ? 'default' : 'secondary'}>
                            {access.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => revokeQuizAccess(access.id, access.quiz.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8">
                    Người dùng này chưa có quyền truy cập quiz nào
                  </p>
                )}
              </div>
            ) : (
              <p className="text-gray-500 text-center py-8">
                Chọn một người dùng để xem quyền truy cập quiz
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

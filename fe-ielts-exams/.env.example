# =============================================================================
# API INTEGRATION
# =============================================================================

# URL của backend API server (chỉ dùng server-side trong Next.js API routes)
# Development: http://localhost:8228, Production: https://api.yourdomai.com
BACKEND_API_URL=http://localhost:8228

# URL công khai của frontend application (dùng cho redirects, metadata)
# Được expose ra browser thông qua NEXT_PUBLIC_ prefix
NEXT_PUBLIC_BASE_URL=http://localhost:3000

# =============================================================================
# ENVIRONMENT CONTROL
# =============================================================================

# Môi trường chạy của ứng dụng (development/staging/production)
# Được expose ra browser để điều khiển behavior khác nhau theo env
NEXT_PUBLIC_NODE_ENV=development

# =============================================================================
# TESTING CONFIGURATION
# =============================================================================

# URL của test/staging environment (dùng khi cần test riêng biệt)
# Port 3001: chỉ dùng khi cần chạy instance thứ 2 để test
NEXT_PUBLIC_TEST_BASE_URL=http://localhost:3001

# URL base cho Playwright E2E testing
# Thường giống NEXT_PUBLIC_BASE_URL nhưng có thể config riêng
PLAYWRIGHT_BASE_URL=http://localhost:3000

# Timeout cho Playwright operations (milliseconds)
# 30000 = 30 giây - thời gian chờ tối đa cho page load và element interactions
PLAYWRIGHT_TIMEOUT=30000

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Tên ứng dụng hiển thị trong UI, metadata, document title
# Được expose ra browser thông qua NEXT_PUBLIC_ prefix
NEXT_PUBLIC_APP_NAME=IELTS Exams

# Timeout cho API calls (milliseconds)
# 10000 = 10 giây - thời gian chờ tối đa cho requests đến backend
NEXT_PUBLIC_API_TIMEOUT=10000
# Frontend Environment Variables
# Copy these values to your main .env.development or .env.production file in root directory

# Environment
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# Backend API Configuration
BACKEND_API_URL=http://localhost:8229
NEXT_PUBLIC_API_URL=http://localhost:8229

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001

# Application Configuration
NEXT_PUBLIC_APP_NAME=IELTS Exams
NEXT_PUBLIC_API_TIMEOUT=10000

# Testing Configuration
PLAYWRIGHT_BASE_URL=http://localhost:3001
PLAYWRIGHT_TIMEOUT=30000
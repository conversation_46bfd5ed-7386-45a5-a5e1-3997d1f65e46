{"name": "nha-tro", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.0", "@playwright/test": "^1.54.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.10.5", "lucide-react": "^0.509.0", "next": "15.3.2", "papaparse": "^5.5.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "xlsx": "^0.18.5", "zod": "^4.0.10"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "tw-animate-css": "^1.2.9", "typescript": "^5"}}
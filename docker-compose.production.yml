# ===========================================
# PRODUCTION ENVIRONMENT - VPS DEPLOYMENT
# ===========================================
# Usage: docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.production.yml up -d

version: '3.8'

services:
  # MySQL - Production specific settings
  mysql:
    container_name: mysql-ielts-prod
    restart: always
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=1G
      --max-connections=300
      --innodb-log-file-size=256M
      --innodb-flush-log-at-trx-commit=2
      --query-cache-size=128M
      --skip-host-cache
      --skip-name-resolve
    environment:
      MYSQL_INITDB_SKIP_TZINFO: 1
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.5'
        reservations:
          memory: 1G
          cpus: '1.0'

  # Backend - Production optimizations
  backend:
    container_name: ielts-backend-prod
    restart: always
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    environment:
      - NODE_OPTIONS=--max-old-space-size=768
      - NODE_ENV=production
      - HOST=0.0.0.0  # Backend cần listen trên tất cả interfaces để frontend container có thể connect
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "5"

  # Frontend - Production optimizations
  frontend:
    container_name: ielts-frontend-prod
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.75'
        reservations:
          memory: 256M
          cpus: '0.5'
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_ENVIRONMENT=production
      - BACKEND_API_URL=http://ielts-backend-prod:${BACKEND_INTERNAL_PORT:-8228}
      - HOSTNAME=0.0.0.0
    logging:
      driver: "json-file"
      options:
        max-size: "30m"
        max-file: "3"

# PhpMyAdmin bị loại bỏ hoàn toàn trong production để bảo mật
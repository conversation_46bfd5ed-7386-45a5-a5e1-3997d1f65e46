# ===========================================
# DEVELOPMENT ENVIRONMENT
# ===========================================

# Environment
NODE_ENV=development
COMPOSE_PROJECT_NAME=ielts-exams-dev

# VPS Configuration
SERVER_DOMAIN=localhost

# Application Ports
FRONTEND_PORT=3001
BACKEND_PORT=8229
BACKEND_INTERNAL_PORT=8228
PHPMYADMIN_PORT=8081

# Database Configuration - Development
MYSQL_ROOT_PASSWORD=dev_root_password_123
MYSQL_DATABASE=ielts_dev_db
MYSQL_USER=ielts_dev_user
MYSQL_PASSWORD=dev_password_123

# JWT Secrets - Development
JWT_SECRET=dev_jwt_secret_key_for_testing_only
JWT_ACCESS_SECRET=dev_access_secret_key_for_testing
JWT_REFRESH_SECRET=dev_refresh_secret_key_for_testing
JWT_PASSWORD_RESET_SECRET=dev_reset_secret_key_for_testing
JWT_EXPIRES_IN=1d

# Development Tools
ENABLE_PHPMYADMIN=true

# Frontend Configuration
NEXT_PUBLIC_ENVIRONMENT=development
NEXT_PUBLIC_API_URL=http://localhost:8229  # Development vẫn cần backend port để test API trực tiếp
NEXT_PUBLIC_APP_URL=http://localhost:3001
BACKEND_API_URL=http://ielts-backend-dev:${BACKEND_INTERNAL_PORT:-8228}
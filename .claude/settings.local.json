{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["mcp__serena__replace_regex", "mcp__serena__initial_instructions", "mcp__serena__get_current_config", "Bash(gh pr:*)", "Bash(gh api:*)", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "Bash(uvx:*)", "mcp__serena__write_memory", "<PERSON><PERSON>(chmod:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__list_dir", "mcp__serena__activate_project", "mcp__serena__find_file", "mcp__serena__get_symbols_overview", "mcp__playwright__browser_navigate", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_evaluate", "mcp__playwright__browser_click", "mcp__serena__find_referencing_symbols", "mcp__serena__replace_symbol_body", "<PERSON><PERSON>(pkill:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(context7)", "mcp__playwright__browser_type", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_snapshot", "Bash(grep:*)", "mcp__playwright__browser_press_key", "mcp__playwright__browser_close", "mcp__playwright__browser_tabs", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_console_messages", "Bash(npm install:*)", "Bash(npm run dev:*)", "Bash(node:*)", "mcp__playwright__browser_drag", "mcp__ctx-compress__compress_context", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_select_option", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/be-ielts-exams diff --name-only)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams diff --name-only)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/be-ielts-exams diff)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams diff)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams diff --stat)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/be-ielts-exams log --oneline -5)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams log --oneline -5)", "Read(//Users/<USER>/**)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/be-ielts-exams status)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams status)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/be-ielts-exams log --oneline -10)", "Bash(git -C /Users/<USER>/Project/Next_JS/ielts-exams/fe-ielts-exams log --oneline -10)", "Bash(npx prisma migrate dev:*)", "Bash(npx prisma:*)", "<PERSON><PERSON>(tsx:*)", "Bash(npx tsx:*)", "<PERSON><PERSON>(curl:*)", "Bash(lsof:*)", "mcp__serena__insert_after_symbol", "mcp__playwright__browser_fill_form", "Bash(ls:*)", "mcp__serena__read_memory", "mcp__serena__think_about_whether_you_are_done", "Bash(npm run lint)", "Bash(find:*)", "<PERSON><PERSON>(npx playwright test:*)", "<PERSON><PERSON>(python3:*)", "Bash(TOKEN=\"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************.n1uxecWFJxj7NjixLPmEVLOxzSCnVr4NtO80wMI5Yv9sVo31P92D9t7kzgwTBRneepLMaKSgdUp5IOVKPBybhAPj9tLYfdMsH39tzg-BD73Mb5zcecLSoOshx_eM90tAsLO3nNbdTcwn7JUI5c1N89PuzNWNciSl3QdBD0S2ZAz3RI0gC3Pr_G2eghZoYNun5yleiCQXtnLkG0kPnxlxcJAdGg2vXvjZP2AGMyeF6yevZj52dHJPXR3O7lehHqzwXGPhRjjCSYLP4XJzpB3e0zJ3BYqpy5YUadmpzClEGZRl_XEJvY_rTjU4pOA9wS9p7VR5UhcLqjM7RURHL-fxAA\")", "mcp__playwright__browser_network_requests", "mcp__serena__think_about_collected_information", "Bash(npm run build:*)", "Bash(npm run start:dev:*)", "Bash(kill:*)", "Bash(xargs:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker ps:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker cp:*)", "Bash(docker volume:*)", "Bash(docker system:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(docker inspect:*)", "Bash(npm outdated)", "Bash(npm --version)", "Bash(git restore:*)", "Bash(npx @prisma/cli@5.15.0:*)", "Bash(npm view:*)", "Bash(npx @prisma/cli@6.16.2:*)", "Bash(NODE_OPTIONS=\"--max-old-space-size=8192\" npx prisma generate)", "Bash(npm ls:*)", "Bash(npx:*)", "Bash(npm cache clean:*)", "<PERSON><PERSON>(env)", "Bash(DATABASE_URL=\"mysql://test:test@localhost:3306/test\" npx prisma generate)", "Bash(rm:*)", "Bash(DATABASE_URL=\"mysql://test:test@localhost:3306/test\" node_modules/.bin/prisma generate)", "Bash(DATABASE_URL=\"mysql://root:password@localhost:3306/ielts_exams\" npx prisma generate)", "Bash(DATABASE_URL=\"mysql://root:password@localhost:3306/ielts_exams\" npx prisma generate --schema=./prisma/schema.prisma)", "Bash(brew services:*)"]}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["serena", "playwright", "ctx-compress"], "disabledMcpjsonServers": ["memory-ai", "<PERSON><PERSON><PERSON>"], "outputStyle": "default"}
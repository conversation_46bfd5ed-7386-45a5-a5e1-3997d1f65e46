# =============================================================================
# SERVER CONFIGURATION
# =============================================================================

# Port mà server sẽ chạy (thường 8228 cho development, 80/443 cho production)
PORT=8228

# Connection string cho MySQL database
# Format: mysql://username:password@host:port/database_name
DATABASE_URL=mysql://app_user:app_pass@localhost:3306/app_db

# Port của MySQL database (default: 3306)
DATABASE_PORT=3306

# =============================================================================
# SECURITY & CRYPTOGRAPHY
# =============================================================================

# Đường dẫn đến RSA private key dùng để ký JWT tokens
# Tạo bằng: openssl genrsa -out keys/key.pem 2048
APP_KEY_PATH=./keys/key.pem

# Đường dẫn đến RSA public key dùng để verify JWT tokens
# Tạo bằng: openssl rsa -in keys/key.pem -pubout -out keys/key.public.pem
APP_PUBLIC_KEY_PATH=./keys/key.public.pem

# =============================================================================
# CORS & FRONTEND INTEGRATION
# =============================================================================

# URL chính của frontend application (dùng cho CORS và redirects)
FRONTEND_URL=http://localhost:3000

# Danh sách các origin được phép truy cập API (cách nhau bởi dấu phẩy)
# Port 3000: frontend chính, Port 3001: test/staging environment (nếu cần)
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# Thời gian sống của CSRF cookie (milliseconds)
# 86400000 = 24 giờ - thời gian CSRF token hợp lệ
CSRF_COOKIE_MAX_AGE=86400000

# Thời gian cửa sổ rate limiting (milliseconds)
# 900000 = 15 phút - reset counter sau mỗi 15 phút
RATE_LIMIT_WINDOW_MS=900000

# Số request tối đa trong production environment
# Giới hạn số lần call API để tránh abuse
RATE_LIMIT_MAX_REQUESTS=100

# Số request tối đa trong development environment (cao hơn để test)
RATE_LIMIT_MAX_REQUESTS_DEV=1000

# Kích thước file upload tối đa (bytes)
# 5242880 = 5MB - giới hạn cho file import quiz JSON
MAX_FILE_SIZE=5242880

# =============================================================================
# TIME CONFIGURATION
# =============================================================================

# Số ngày token hết hạn (dùng cho user quiz access)
# 30 ngày = thời gian học viên có thể truy cập quiz sau khi được cấp quyền
TOKEN_EXPIRY_DAYS=30

# Số giờ password reset token hợp lệ
# 1 giờ = thời gian user phải reset password sau khi request
PASSWORD_RESET_HOURS=1

# Số giờ CSRF token hợp lệ
# 24 giờ = thời gian session security token tồn tại
CSRF_TOKEN_HOURS=24

# Số phút cho rate limiting window (không dùng trực tiếp, chỉ để reference)
RATE_LIMIT_MINUTES=15

# Số giờ cho authentication rate limiting
# 1 giờ = thời gian block sau khi login/register quá nhiều lần
RATE_LIMIT_AUTH_HOURS=1

# =============================================================================
# JWT AUTHENTICATION
# =============================================================================

# Secret key để ký access tokens (phải thay đổi trong production!)
# Access token có thời gian sống ngắn, dùng cho xác thực API calls
JWT_ACCESS_SECRET=your_jwt_access_secret_here

# Secret key để ký refresh tokens (phải thay đổi trong production!)
# Refresh token có thời gian sống dài, dùng để tạo access token mới
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here

# Secret key để ký password reset tokens (phải thay đổi trong production!)
# Dùng riêng cho reset password để tăng security
JWT_PASSWORD_RESET_SECRET=your_jwt_password_reset_secret_here

# Thời gian sống của access token
# 1d = 1 ngày - user không cần login lại trong 1 ngày
JWT_ACCESS_EXPIRES_IN=1d

# Thời gian sống của refresh token
# 7d = 7 ngày - user có thể refresh access token trong 7 ngày
JWT_REFRESH_EXPIRES_IN=7d

# Thời gian sống của password reset token
# 1h = 1 giờ - link reset password chỉ hợp lệ 1 giờ
JWT_PASSWORD_RESET_EXPIRES_IN=1h

# =============================================================================
# HTTP SECURITY HEADERS
# =============================================================================

# HTTP Strict Transport Security max age (seconds)
# 31536000 = 1 năm - browser chỉ kết nối qua HTTPS trong 1 năm
HSTS_MAX_AGE=31536000

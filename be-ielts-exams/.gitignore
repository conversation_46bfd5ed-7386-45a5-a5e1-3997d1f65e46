# compiled output
/dist
/node_modules

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
.env
.env.local
.env.production
.env.staging

# Security keys (CRITICAL: never commit these!)
*.pem
*.key
*.crt
*.p12
*.pfx
jwt-keys/
keys/
secrets/

/public/uploads/tmp/
/.claude
/.mcp.json
/memory_bank
/.trae
/.cursor
/.serena
forge.yaml
/plans
/example
/docs/ui_example
CLAUDE.md
mcp.json
.agent.md
.qoder/rules/common.md
build-admin-task.md
pastmax.md
WARP.md
/.vscode
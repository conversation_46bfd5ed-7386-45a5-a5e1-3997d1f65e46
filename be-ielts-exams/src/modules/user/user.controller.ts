import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Put, Query, UseGuards } from "@nestjs/common";
import { GetCurrentUser } from "src/decorators";
import { AccessTokenGuard } from "src/guards";
import RoleGuard from "src/guards/admin-role.guard";
import { EditUserDto, CreateUserDto, GetUserDto, ChangeUserPasswordDto } from "./dto";
import { UserService } from "./user.service";

@Controller({
  path: "/users",
  version: "1"
})
export class UserController {
  constructor(private userService: UserService) {}

  @Post()
  @UseGuards(AccessTokenGuard)
  create(@Body() createUserDto: CreateUserDto) {
    return this.userService.create(createUserDto);
  }

  @Post("changePassword/:id")
  @HttpCode(HttpStatus.OK)
  // @UseGuards(RoleGuard("ADMIN"))
  @UseGuards(AccessTokenGuard)
  changePasswordUser(@Param("id") uid: string, @Body() changePasswordDto: ChangeUserPasswordDto): Promise<boolean> {
    return this.userService.changePasswordUser(uid, changePasswordDto.newPassword);
  }

  @Get("me")
  @UseGuards(AccessTokenGuard)
  getMe(@GetCurrentUser("id") userId: string) {
    return this.userService.findById(userId);
  }

  @Get()
  @UseGuards(AccessTokenGuard)
  getUserList(@Query() query: GetUserDto) {
    return this.userService.getUserList(query);
  }

  @Get(":id")
  @UseGuards(AccessTokenGuard)
  getUserById(@Param("id") id: string) {
    return this.userService.findById(id);
  }

  @Put("me")
  @UseGuards(AccessTokenGuard, RoleGuard(["ADMIN", "USER"]))
  @UseGuards(AccessTokenGuard)
  updateMe(@GetCurrentUser("id") userId: string, @Body() dto: EditUserDto) {
    return this.userService.editUser(userId, dto);
  }

  @Put(":id")
  // @UseInterceptors(new ActionLogInterceptor(ModalType.USER, ActionType.UPDATE))
  @UseGuards(RoleGuard("ADMIN"))
  @UseGuards(AccessTokenGuard)
  editUser(@Param("id") uid: string, @Body() dto: EditUserDto) {
    return this.userService.editUser(uid, dto);
  }
}

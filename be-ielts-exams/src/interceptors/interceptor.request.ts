import type { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, NestInterceptor } from "@nestjs/common";
import { Injectable } from "@nestjs/common";
import { randomUUID } from "crypto";

@Injectable()
export class RequestInterceptor implements NestInterceptor {
  async intercept(context: ExecutionContext, next: CallHandler) {
    const request = context.switchToHttp().getRequest();
    request.id = request.id || randomUUID();
    return next.handle();
  }
}

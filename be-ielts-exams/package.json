{"name": "server_template", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.4.20", "@nestjs/config": "3.1.1", "@nestjs/core": "^10.4.20", "@nestjs/jwt": "10.1.0", "@nestjs/passport": "10.0.1", "@nestjs/platform-express": "^10.4.20", "@nestjs/schedule": "6.0.0", "@nestjs/serve-static": "^5.0.3", "@prisma/client": "^6.16.1", "@types/multer": "^2.0.0", "@types/passport-jwt": "3.0.10", "argon2": "^0.44.0", "cache-manager": "5.2.4", "class-transformer": "0.5.1", "class-validator": "^0.14.2", "helmet": "^8.1.0", "multer": "^2.0.2", "passport": "0.6.0", "passport-jwt": "4.0.1", "passport-local": "1.0.0", "reflect-metadata": "^0.1.14", "rimraf": "^6.0.1", "rxjs": "^7.8.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.35.0", "@nestjs/cli": "^10.4.9", "@nestjs/schematics": "10.0.0", "@nestjs/testing": "10.3.8", "@types/cron": "2.4.3", "@types/express": "4.17.21", "@types/helmet": "^0.0.48", "@types/jest": "^30.0.0", "@types/node": "20.11.30", "@types/passport-jwt": "3.0.10", "@types/passport-local": "1.0.36", "@types/supertest": "6.0.3", "@typescript-eslint/eslint-plugin": "^8.43.0", "@typescript-eslint/parser": "^8.43.0", "eslint": "^9.35.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "jest": "^30.1.3", "prettier": "^3.6.2", "prisma": "^6.16.1", "source-map-support": "0.5.21", "supertest": "7.1.4", "ts-jest": "^29.4.1", "ts-loader": "^9.5.4", "ts-node": "10.9.2", "tsconfig-paths": "4.2.0", "typescript": "^5.9.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}, "prisma": {"seed": "ts-node prisma/seed.ts"}}
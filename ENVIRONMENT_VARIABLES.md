# 🌍 Environment Variables Reference

Tài liệu tổng hợp về tất cả environment variables trong hệ thống IELTS Exams.

## 📋 Tổng quan

Hệ thống sử dụng environment variables để:
- **Loại bỏ hardcode**: Tất cả URLs, timeouts, limits đượ<PERSON> config qua env vars
- **Tăng tính linh hoạt**: <PERSON><PERSON> dàng deploy trên các môi trường khác nhau
- **B<PERSON><PERSON> mật**: Sensitive data như JWT secrets không hardcode trong code
- **Maintainability**: Centralized configuration management

## 🌐 Port Allocation

**Port Chính:**
- **Frontend**: `3000` - Port chính cho Next.js development
- **Backend**: `8228` - Port chính cho NestJS API

**Port Phụ (khi cần):**
- **Frontend Test**: `3001` - Chỉ dùng khi cần chạy instance thứ 2 để test
- **Database**: `3306` - MySQL default port
- **Production**: `80/443` - HTTP/HTTPS standard ports

## 🔧 Backend Environment Variables

### Server Configuration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `PORT` | Port mà server sẽ chạy | `8228` | `8228`, `80`, `443` |
| `DATABASE_URL` | Connection string cho MySQL | - | `mysql://user:pass@host:3306/db` |
| `DATABASE_PORT` | Port của MySQL database | `3306` | `3306`, `3307` |

### Security & Cryptography
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `APP_KEY_PATH` | Đường dẫn RSA private key | `./keys/key.pem` | `./keys/production.pem` |
| `APP_PUBLIC_KEY_PATH` | Đường dẫn RSA public key | `./keys/key.public.pem` | `./keys/production.public.pem` |

### CORS & Frontend Integration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `FRONTEND_URL` | URL chính của frontend | `http://localhost:3000` | `https://ielts.yourdomain.com` |
| `CORS_ORIGIN` | Danh sách origins được phép (cách nhau bởi dấu phẩy). Port 3000 là chính, 3001 cho test | `http://localhost:3000,http://localhost:3001` | `https://app.com,https://staging.app.com` |

### Security Configuration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `CSRF_COOKIE_MAX_AGE` | Thời gian sống CSRF cookie (ms) | `86400000` (24h) | `43200000` (12h) |
| `RATE_LIMIT_WINDOW_MS` | Thời gian window rate limiting (ms) | `900000` (15min) | `600000` (10min) |
| `RATE_LIMIT_MAX_REQUESTS` | Số request tối đa (production) | `100` | `200`, `50` |
| `RATE_LIMIT_MAX_REQUESTS_DEV` | Số request tối đa (development) | `1000` | `2000`, `500` |
| `MAX_FILE_SIZE` | Kích thước file upload tối đa (bytes) | `5242880` (5MB) | `10485760` (10MB) |

### Time Configuration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `TOKEN_EXPIRY_DAYS` | Số ngày token hết hạn | `30` | `60`, `15` |
| `PASSWORD_RESET_HOURS` | Số giờ password reset token hợp lệ | `1` | `2`, `0.5` |
| `CSRF_TOKEN_HOURS` | Số giờ CSRF token hợp lệ | `24` | `12`, `48` |
| `RATE_LIMIT_AUTH_HOURS` | Số giờ block auth sau abuse | `1` | `2`, `0.5` |

### JWT Authentication
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `JWT_ACCESS_SECRET` | Secret key cho access tokens | - | `your-super-secret-key-256-bits` |
| `JWT_REFRESH_SECRET` | Secret key cho refresh tokens | - | `your-refresh-secret-key-256-bits` |
| `JWT_PASSWORD_RESET_SECRET` | Secret key cho password reset | - | `your-reset-secret-key-256-bits` |
| `JWT_ACCESS_EXPIRES_IN` | Thời gian sống access token | `1d` | `15m`, `2h`, `7d` |
| `JWT_REFRESH_EXPIRES_IN` | Thời gian sống refresh token | `7d` | `30d`, `3d` |
| `JWT_PASSWORD_RESET_EXPIRES_IN` | Thời gian sống reset token | `1h` | `30m`, `2h` |

### HTTP Security Headers
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `HSTS_MAX_AGE` | HTTPS-only duration (seconds) | `31536000` (1 year) | `15768000` (6 months) |

## 💻 Frontend Environment Variables

### API Integration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `BACKEND_API_URL` | URL backend API (server-side only) | `http://localhost:8228` | `https://api.yourdomain.com` |
| `NEXT_PUBLIC_BASE_URL` | URL công khai frontend | `http://localhost:3000` | `https://ielts.yourdomain.com` |

### Environment Control
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `NEXT_PUBLIC_NODE_ENV` | Environment mode | `development` | `production`, `staging` |

### Testing Configuration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `NEXT_PUBLIC_TEST_BASE_URL` | URL test environment (chỉ khi cần instance thứ 2) | `http://localhost:3001` | `https://staging.yourdomain.com` |
| `PLAYWRIGHT_BASE_URL` | URL cho Playwright tests | `http://localhost:3000` | `http://localhost:3001` |
| `PLAYWRIGHT_TIMEOUT` | Timeout Playwright (ms) | `30000` (30s) | `60000` (60s) |

### Application Configuration
| Variable | Ý nghĩa | Default | Ví dụ |
|----------|---------|---------|-------|
| `NEXT_PUBLIC_APP_NAME` | Tên ứng dụng hiển thị | `IELTS Exams` | `IELTS Pro`, `My IELTS` |
| `NEXT_PUBLIC_API_TIMEOUT` | Timeout API calls (ms) | `10000` (10s) | `15000` (15s) |

## 🚀 Setup Instructions

### 1. Backend Setup
```bash
cd be-ielts-exams
cp .env.example .env
# Chỉnh sửa .env với values phù hợp
nano .env
```

### 2. Frontend Setup
```bash
cd fe-ielts-exams
cp .env.example .env.local
# Chỉnh sửa .env.local với values phù hợp
nano .env.local
```

### 3. Generate JWT Secrets (Backend)
```bash
# Tạo random secrets cho production
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

### 4. Generate RSA Keys (Backend)
```bash
# Tạo RSA key pair
mkdir -p keys
openssl genrsa -out keys/key.pem 2048
openssl rsa -in keys/key.pem -pubout -out keys/key.public.pem
```

## 🌐 Environment-Specific Examples

### Development
```env
# Backend (.env)
PORT=8228
FRONTEND_URL=http://localhost:3000
JWT_ACCESS_EXPIRES_IN=1d
RATE_LIMIT_MAX_REQUESTS_DEV=1000

# Frontend (.env.local)
BACKEND_API_URL=http://localhost:8228
NEXT_PUBLIC_NODE_ENV=development
```

### Staging
```env
# Backend (.env)
PORT=8228
FRONTEND_URL=https://staging.yourdomain.com
JWT_ACCESS_EXPIRES_IN=2h
RATE_LIMIT_MAX_REQUESTS=200

# Frontend (.env.local)
BACKEND_API_URL=https://api-staging.yourdomain.com
NEXT_PUBLIC_NODE_ENV=staging
```

### Production
```env
# Backend (.env)
PORT=80
FRONTEND_URL=https://ielts.yourdomain.com
JWT_ACCESS_EXPIRES_IN=15m
RATE_LIMIT_MAX_REQUESTS=100
HSTS_MAX_AGE=31536000

# Frontend (.env.local)
BACKEND_API_URL=https://api.yourdomain.com
NEXT_PUBLIC_NODE_ENV=production
```

## 🔒 Security Best Practices

1. **Never commit .env files** - Luôn add vào .gitignore
2. **Use strong JWT secrets** - Minimum 256 bits random
3. **Rotate secrets regularly** - Especially in production
4. **Validate environment** - Check required vars on startup
5. **Use different secrets per environment** - Dev/staging/prod khác nhau
6. **Monitor rate limits** - Adjust based on actual usage
7. **Use HTTPS in production** - Set secure flags properly

## 🐛 Troubleshooting

### Backend Issues
- **Build fails**: Check environment variables syntax
- **JWT errors**: Verify secrets are properly set
- **CORS errors**: Check FRONTEND_URL and CORS_ORIGIN
- **Rate limit issues**: Adjust RATE_LIMIT_* variables

### Frontend Issues
- **API timeouts**: Increase NEXT_PUBLIC_API_TIMEOUT
- **Build errors**: Verify NEXT_PUBLIC_ variables
- **Playwright failures**: Check PLAYWRIGHT_* settings

## 📝 Notes

- **NEXT_PUBLIC_ prefix**: Variables được expose ra browser
- **No prefix**: Variables chỉ dùng server-side
- **Type conversion**: parseInt() cho number values
- **Array handling**: Split comma-separated strings
- **Fallback values**: Always provide sensible defaults

---

**⚠️ Important**: Luôn review và test environment variables trước khi deploy production!
# ===========================================
# DEVELOPMENT ENVIRONMENT - VPS TESTING
# ===========================================
# Usage: docker compose --env-file .env.development -f docker-compose.yml -f docker-compose.development.yml --profile dev up -d 

# Set role admin for user
# docker exec mysql-ielts-dev mysql -u ielts_dev_user -pdev_password_123
#       ielts_dev_db -e "UPDATE User SET role = 'ADMIN' WHERE username = 'admin'
#       OR email = '<EMAIL>';"
version: '3.8'

services:
  # MySQL - Development settings with debug
  mysql:
    container_name: mysql-ielts-dev
    restart: unless-stopped
    ports:
      - "3307:3306"  # Expose MySQL port for development debugging
    command: >
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --general-log=1
      --general-log-file=/var/lib/mysql/general.log
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # PhpMyAdmin for development
  phpmyadmin:
    container_name: phpmyadmin-ielts-dev
    restart: unless-stopped
    ports:
      - "${PHPMYADMIN_PORT:-8080}:80"
    profiles:
      - dev
      - debug
      - default

  # Backend - Development with debug features
  backend:
    container_name: ielts-backend-dev
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.75'
        reservations:
          memory: 256M
          cpus: '0.5'
    environment:
      - NODE_OPTIONS=--max-old-space-size=384 --inspect=0.0.0.0:9229
      - NODE_ENV=development
    ports:
      - "${BACKEND_PORT:-8229}:8228"  # Development vẫn expose backend để test API
      - "9229:9229"  # Debug port
    logging:
      driver: "json-file"
      options:
        max-size: "20m"
        max-file: "3"

  # Frontend - Development with hot reload
  frontend:
    container_name: ielts-frontend-dev
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 384M
          cpus: '0.5'
        reservations:
          memory: 192M
          cpus: '0.25'
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_ENVIRONMENT=development
      - BACKEND_API_URL=http://ielts-backend-dev:${BACKEND_INTERNAL_PORT:-8228}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "2"
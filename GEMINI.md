# Project: IELTS Exams Platform
## Rules
This file provides guidance to <PERSON> when working with code in this repository.
  - Keep source code and files under 500 lines.
  - If the being generated file is too big, generate small pieces.
  - Do not refactor code unless necessary. If you need to refactor, please do it in a separate task, only refactor if I ask you to do so.
  - If you want to: Explore and Learn Codebase, Work with Symbols, Knowledge Management, Workflow. Prioritize using mcp tool serena first
  - Run mcp tool ctx-compress to compress context if the following requirements are met:
    - token is too long, need to be reduced to save context
    - if the document is not related to the codebase
    - after calling other mcp tools, tools, the output is too long, need to be compressed (do not compress the codebase)
    note: absolutely do not compress anything related to code, codebase
  - when you work with nextjs project always use playwright mcp to test before complete task, i alway run fe server at port 3000 and be server at port 8228, so you can not re-run server
## Authentication Information
- User: nguy<PERSON><EMAIL> / password123
- Admin: admin / admin123
- Backend Port: 8228
- Frontend Port: 3000


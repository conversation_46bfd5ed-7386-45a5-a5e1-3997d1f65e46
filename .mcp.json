{"mcpServers": {"serena": {"command": "/Users/<USER>/.local/bin/uv", "args": ["run", "--directory", "/Users/<USER>/Tools/MCP/serena", "serena", "start-mcp-server", "--context", "ide-assistant", "--project", "$(pwd)"]}, "playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "ctx-compress": {"command": "npx", "args": ["-y", "@daothihuong2111/context-compression-mcp", "--api-key", "AIzaSyDdCaoNLww5dMUV9mWfOvm7G2XL2lSpQds", "--model", "gemini-2.5-flash"]}}}
# Project Organization & Folder Structure

## Monorepo Layout
```
ielts-exams/
├── fe-ielts-exams/          # Next.js frontend application
├── be-ielts-exams/          # NestJS backend API
├── scripts/                 # Shared utility scripts
├── templates/               # Project templates
├── memory/                  # Documentation and guides
├── .kiro/                   # Kiro IDE configuration
└── .serena/                 # Serena AI assistant memories
```

## Frontend Structure (fe-ielts-exams/)

### Core Application
```
src/
├── app/                     # Next.js App Router pages
│   ├── (web)/              # Public-facing pages route group
│   ├── (admin)/            # Admin dashboard route group
│   ├── api/                # API routes (proxy to backend)
│   ├── globals.css         # Global styles
│   └── layout.tsx          # Root layout component
├── components/             # Reusable UI components
│   ├── ui/                 # Base UI components (Radix/shadcn)
│   ├── home/               # Home page components
│   ├── auth/               # Authentication components
│   ├── exam/               # Exam interface components
│   └── admin/              # Admin dashboard components
├── contexts/               # React contexts
│   └── auth-context.tsx    # Authentication state management
├── hooks/                  # Custom React hooks
├── lib/                    # Utility libraries
│   ├── utils.ts            # General utilities
│   └── axios.ts            # HTTP client configuration
├── services/               # API service layer
├── types/                  # TypeScript type definitions
├── utils/                  # Helper functions
└── middleware.ts           # Next.js middleware
```

### Configuration & Testing
```
├── tests/                  # Playwright E2E tests
│   ├── auth/               # Authentication tests
│   ├── data/               # Test data
│   └── utils/              # Test utilities
├── public/                 # Static assets
├── playwright.config.ts    # Playwright configuration
├── next.config.ts          # Next.js configuration
├── tailwind.config.js      # TailwindCSS configuration
└── components.json         # shadcn/ui configuration
```

## Backend Structure (be-ielts-exams/)

### Core Application
```
src/
├── modules/                # Feature modules (NestJS pattern)
│   ├── auth/               # Authentication module
│   │   ├── auth.controller.ts
│   │   ├── auth.service.ts
│   │   ├── auth.module.ts
│   │   ├── dto/            # Data Transfer Objects
│   │   └── strategies/     # Passport strategies
│   ├── user/               # User management module
│   │   ├── user.controller.ts
│   │   ├── user.service.ts
│   │   ├── user.module.ts
│   │   └── dto/
│   ├── prisma/             # Database module
│   │   ├── prisma.service.ts
│   │   └── prisma.module.ts
│   └── quiz/               # Quiz/exam module
├── common/                 # Shared utilities
│   ├── utils.ts            # Helper functions
│   └── password-validator.ts
├── config/                 # Configuration files
│   ├── configuration.ts    # App configuration
│   └── security.config.ts  # Security settings
├── decorators/             # Custom decorators
│   ├── get-current-user.decorator.ts
│   └── unique-field.decorator.ts
├── dto/                    # Global DTOs
├── filters/                # Exception filters
│   └── http-exception.filter.ts
├── guards/                 # Authentication guards
│   ├── access-token.guard.ts
│   ├── refresh-token.guard.ts
│   └── admin-role.guard.ts
├── interceptors/           # Request/response interceptors
│   ├── interceptor.request.ts
│   └── interceptor.transform-response.ts
├── middleware/             # Custom middleware
│   ├── app-logger.middleware.ts
│   ├── csrf.middleware.ts
│   ├── rate-limit.middleware.ts
│   └── sanitization.middleware.ts
├── seeds/                  # Database seeding
│   ├── data/               # Seed data
│   ├── common/             # Seed utilities
│   ├── user-admin.seed.ts
│   └── index.ts
├── app.module.ts           # Root application module
└── main.ts                 # Application bootstrap
```

### Database & Configuration
```
├── prisma/                 # Database schema and migrations
│   └── schema.prisma       # Prisma schema definition
├── keys/                   # JWT RSA keys (generated, not in repo)
│   ├── key.pem             # Private key
│   └── key.public.pem      # Public key
├── test/                   # E2E tests
├── coverage/               # Test coverage reports
├── dist/                   # Compiled output
├── generate-keys.sh        # JWT key generation script
└── .env.example            # Environment template
```

## Key Architectural Patterns

### Frontend Patterns
- **App Router**: Next.js 13+ file-based routing with route groups
- **Component Organization**: Feature-based component grouping
- **Context Pattern**: Centralized state management for auth
- **Service Layer**: Axios-based API abstraction
- **Type Safety**: Comprehensive TypeScript coverage

### Backend Patterns
- **Module Pattern**: NestJS modular architecture
- **Controller-Service Pattern**: Separation of concerns
- **DTO Pattern**: Data validation and transformation
- **Guard Pattern**: Authentication and authorization
- **Middleware Pattern**: Cross-cutting concerns
- **Repository Pattern**: Database abstraction through Prisma

### Security Architecture
- **JWT Strategy**: Access/refresh token pattern with RSA signing
- **httpOnly Cookies**: Secure token storage
- **Role-based Access**: ADMIN/USER role separation
- **Input Validation**: class-validator with custom rules
- **Security Middleware**: Rate limiting, CORS, sanitization

### Testing Structure
- **Frontend**: Playwright E2E tests with comprehensive auth coverage
- **Backend**: Jest unit tests and E2E tests
- **Test Organization**: Feature-based test grouping
- **Test Data**: Centralized test data management
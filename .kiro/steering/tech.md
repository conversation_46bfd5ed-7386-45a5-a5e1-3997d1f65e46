# Technology Stack & Build System

## Architecture Overview
Full-stack monorepo with separate frontend and backend applications.

## Frontend (fe-ielts-exams)

### Core Technologies
- **Framework**: Next.js 15 with App Router
- **Runtime**: React 19
- **Language**: TypeScript 5
- **Styling**: TailwindCSS 4
- **UI Components**: Radix UI + shadcn/ui components
- **Forms**: React Hook Form + Zod validation
- **HTTP Client**: Axios with interceptors
- **Animation**: Framer Motion
- **Charts**: Recharts
- **Icons**: Lucide React
- **Testing**: Playwright E2E testing

### Build System & Commands
```bash
# Development (with Turbopack)
npm run dev

# Production build
npm run build

# Start production server
npm start

# Code quality
npm run lint
```

### Testing Commands
```bash
# Run all E2E tests
npx playwright test

# Run specific test file
npx playwright test tests/auth/auth.e2e.spec.ts

# Run tests with visible browser
npx playwright test --headed

# Debug mode
npx playwright test --debug

# View test reports
npx playwright show-report
```

## Backend (be-ielts-exams)

### Core Technologies
- **Framework**: NestJS 10
- **Language**: TypeScript
- **Database**: MySQL with Prisma ORM
- **Authentication**: JWT with RSA signing + httpOnly cookies
- **Password Hashing**: Argon2
- **Validation**: class-validator + class-transformer
- **Security**: Helmet, CORS, rate limiting, input sanitization
- **Testing**: Jest (unit & E2E)
- **Package Manager**: Yarn

### Build System & Commands
```bash
# Development (watch mode)
yarn start:dev

# Debug mode
yarn start:debug

# Production build
yarn build

# Start production server
yarn start:prod

# Code quality
yarn lint
yarn format
```

### Testing Commands
```bash
# All unit tests
yarn test

# Watch mode
yarn test:watch

# Test coverage
yarn test:cov

# E2E tests
yarn test:e2e

# Debug tests
yarn test:debug
```

### Database Commands
```bash
# Generate Prisma client
npx prisma generate

# Apply schema changes (dev)
npx prisma db push

# Create migration
npx prisma migrate dev --name <name>

# Apply migrations (production)
npx prisma migrate deploy

# Database GUI
npx prisma studio
```

## Security Setup

### JWT Keys (Critical)
```bash
# Generate RSA key pairs (required after clone)
cd be-ielts-exams
./generate-keys.sh

# Manual generation
mkdir -p keys
openssl genrsa -out keys/key.pem 2048
openssl rsa -in keys/key.pem -outform PEM -pubout -out keys/key.public.pem
```

### Environment Secrets
```bash
# Generate secure JWT secrets
JWT_ACCESS_SECRET=$(openssl rand -base64 32)
JWT_REFRESH_SECRET=$(openssl rand -base64 32)
JWT_PASSWORD_RESET_SECRET=$(openssl rand -base64 32)
```

## Development Workflow

### Initial Setup
```bash
# 1. Frontend setup
cd fe-ielts-exams
npm install
cp .env.example .env.local

# 2. Backend setup
cd ../be-ielts-exams
yarn install
./generate-keys.sh  # Generate JWT keys (REQUIRED)
cp .env.example .env # Edit with database config
npx prisma generate
npx prisma db push

# 3. Start development servers
# Terminal 1 - Backend (port 8228)
yarn start:dev

# Terminal 2 - Frontend (port 3000)
cd ../fe-ielts-exams
npm run dev
```

### Code Quality Tools
- **ESLint**: TypeScript linting with Prettier integration
- **Prettier**: Code formatting
- **TypeScript**: Strict type checking
- **Husky**: Git hooks for pre-commit checks (frontend)

## Key Configuration Files
- **Frontend**: `next.config.ts`, `tsconfig.json`, `tailwind.config.js`
- **Backend**: `nest-cli.json`, `tsconfig.json`, `prisma/schema.prisma`
- **Environment**: `.env.example` files in both projects
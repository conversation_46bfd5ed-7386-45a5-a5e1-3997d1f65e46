# Requirements Document

## Introduction

Dự án này nhằm mục đích migrate hệ thống quiz hiện tại từ localStorage (frontend) lên database MySQL (backend). Hiện tại, toàn bộ dữ liệu quiz được lưu trữ và quản lý ở phía frontend thông qua localStorage, bao gồm cả hệ thống admin để tạo và chỉnh sửa quiz. Mục tiêu là chuyển đổi sang kiến trúc client-server với database tập trung, đồng thời giữ nguyên tính năng và trải nghiệm người dùng.

## Requirements

### Requirement 1: Database Schema Design

**User Story:** Là một developer, tôi muốn có database schema phù hợp để lưu trữ dữ liệu quiz phức tạp và quản lý access control, để có thể quản lý hiệu quả cả cấu trúc SQL và JSON cùng với phân quyền user.

#### Acceptance Criteria

1. WHEN thiết kế database schema THEN hệ thống SHALL hỗ trợ cả hai phương pháp lưu trữ: SQL normalized và JSON storage
2. WHEN lưu trữ quiz data THEN hệ thống SHALL bảo toàn toàn bộ cấu trúc dữ liệu hiện tại của MultiPartQuiz interface
3. WHEN thiết kế schema THEN hệ thống SHALL hỗ trợ các loại câu hỏi: MULTIPLE_CHOICE, TRUE_FALSE_NOTGIVEN, SENTENCE_COMPLETION, PARAGRAPH_MATCHING_TABLE, DRAG_AND_DROP, TABLE_COMPLETION, MULTIPLE_SELECT, MATCHING_TABLE
4. WHEN lưu trữ quiz THEN hệ thống SHALL hỗ trợ cả Reading và Listening quiz types
5. WHEN thiết kế database THEN hệ thống SHALL có khả năng mở rộng cho các loại quiz mới trong tương lai
6. WHEN thiết kế schema THEN hệ thống SHALL hỗ trợ many-to-many relationship giữa User và Quiz để quản lý access permissions
7. WHEN admin assign quiz THEN hệ thống SHALL lưu trữ thông tin phân quyền với timestamps và metadata

### Requirement 2: Backend API Development

**User Story:** Là một frontend developer, tôi muốn có REST API để quản lý quiz với access control, để có thể thay thế các localStorage operations hiện tại và kiểm soát quyền truy cập.

#### Acceptance Criteria

1. WHEN tạo API endpoints THEN hệ thống SHALL cung cấp CRUD operations cho quiz management
2. WHEN gọi API GET /quizzes THEN hệ thống SHALL trả về danh sách quiz mà user có quyền truy cập với pagination
3. WHEN gọi API GET /quizzes/:id THEN hệ thống SHALL trả về chi tiết quiz nếu user có permission
4. WHEN gọi API POST /quizzes THEN hệ thống SHALL tạo quiz mới (ADMIN only)
5. WHEN gọi API PUT /quizzes/:id THEN hệ thống SHALL cập nhật quiz (ADMIN only)
6. WHEN gọi API DELETE /quizzes/:id THEN hệ thống SHALL xóa quiz (ADMIN only)
7. WHEN gọi API POST /quizzes/:id/assign THEN hệ thống SHALL assign quiz cho users (ADMIN only)
8. WHEN gọi API DELETE /quizzes/:id/users/:userId THEN hệ thống SHALL revoke quiz access (ADMIN only)
9. WHEN gọi API THEN hệ thống SHALL validate dữ liệu input theo MultiPartQuiz interface
10. WHEN xử lý request THEN hệ thống SHALL có proper error handling và status codes
11. WHEN user không có permission THEN hệ thống SHALL trả về 403 Forbidden error

### Requirement 3: Data Migration Strategy

**User Story:** Là một admin, tôi muốn migrate dữ liệu quiz hiện có từ localStorage lên database, để không mất dữ liệu đã tạo.

#### Acceptance Criteria

1. WHEN thực hiện migration THEN hệ thống SHALL đọc tất cả quiz data từ localStorage
2. WHEN migrate data THEN hệ thống SHALL validate và transform data theo database schema
3. WHEN migration hoàn thành THEN hệ thống SHALL verify data integrity
4. WHEN có lỗi migration THEN hệ thống SHALL rollback và báo lỗi chi tiết
5. WHEN migration thành công THEN hệ thống SHALL tạo backup của localStorage data
6. IF có duplicate data THEN hệ thống SHALL handle conflicts appropriately

### Requirement 4: Frontend Integration

**User Story:** Là một user, tôi muốn hệ thống quiz hoạt động như trước, để không bị gián đoạn trải nghiệm sử dụng.

#### Acceptance Criteria

1. WHEN load quiz THEN frontend SHALL gọi API thay vì đọc từ localStorage
2. WHEN submit quiz answers THEN frontend SHALL gửi kết quả lên server
3. WHEN admin tạo/sửa quiz THEN frontend SHALL sử dụng API thay vì localStorage
4. WHEN có network error THEN frontend SHALL hiển thị error message phù hợp
5. WHEN API response THEN frontend SHALL handle loading states properly
6. WHEN migrate frontend THEN hệ thống SHALL remove tất cả localStorage dependencies

### Requirement 5: Admin System Enhancement

**User Story:** Là một admin, tôi muốn hệ thống admin hoạt động với database backend và quản lý user permissions, để quản lý quiz tập trung và kiểm soát quyền truy cập hiệu quả.

#### Acceptance Criteria

1. WHEN admin login THEN hệ thống SHALL verify admin role từ database
2. WHEN admin tạo quiz THEN hệ thống SHALL lưu vào database thay vì localStorage
3. WHEN admin edit quiz THEN hệ thống SHALL update database và sync real-time
4. WHEN admin delete quiz THEN hệ thống SHALL xóa từ database với confirmation và revoke tất cả user permissions
5. WHEN admin import/export quiz THEN hệ thống SHALL work với database data
6. WHEN admin preview quiz THEN hệ thống SHALL load từ database
7. WHEN multiple admins THEN hệ thống SHALL handle concurrent editing
8. WHEN admin assign quiz THEN hệ thống SHALL có interface để select users và assign permissions
9. WHEN admin view quiz permissions THEN hệ thống SHALL hiển thị danh sách users có quyền truy cập
10. WHEN admin revoke permissions THEN hệ thống SHALL remove user access với confirmation
11. WHEN admin bulk assign THEN hệ thống SHALL hỗ trợ assign quiz cho multiple users cùng lúc

### Requirement 6: Performance and Caching

**User Story:** Là một user, tôi muốn hệ thống load nhanh và responsive, để có trải nghiệm tốt khi làm quiz.

#### Acceptance Criteria

1. WHEN load quiz list THEN hệ thống SHALL implement caching strategy
2. WHEN load quiz detail THEN hệ thống SHALL optimize database queries
3. WHEN có nhiều users THEN hệ thống SHALL handle concurrent access
4. WHEN quiz data lớn THEN hệ thống SHALL implement pagination
5. WHEN network slow THEN hệ thống SHALL có loading indicators
6. IF database unavailable THEN hệ thống SHALL có fallback mechanism

### Requirement 7: Data Validation and Security

**User Story:** Là một system administrator, tôi muốn đảm bảo data integrity và security, để bảo vệ hệ thống khỏi invalid data và unauthorized access.

#### Acceptance Criteria

1. WHEN receive quiz data THEN hệ thống SHALL validate theo MultiPartQuiz schema
2. WHEN admin operations THEN hệ thống SHALL verify admin permissions
3. WHEN store sensitive data THEN hệ thống SHALL implement proper encryption
4. WHEN API calls THEN hệ thống SHALL implement rate limiting
5. WHEN validate input THEN hệ thống SHALL sanitize data để prevent injection
6. WHEN error occurs THEN hệ thống SHALL log appropriately without exposing sensitive info

### Requirement 8: User Access Control System

**User Story:** Là một user, tôi muốn chỉ thấy và truy cập những quiz mà admin đã cho phép, để có trải nghiệm phù hợp với quyền hạn của mình.

#### Acceptance Criteria

1. WHEN user login THEN hệ thống SHALL chỉ hiển thị quiz mà user có permission
2. WHEN user access quiz THEN hệ thống SHALL verify permission trước khi cho phép
3. WHEN user không có permission THEN hệ thống SHALL hiển thị thông báo lỗi phù hợp
4. WHEN admin assign quiz THEN user SHALL có thể access quiz ngay lập tức
5. WHEN admin revoke permission THEN user SHALL mất quyền truy cập ngay lập tức
6. WHEN user view quiz list THEN hệ thống SHALL filter theo permissions và pagination
7. WHEN user search quiz THEN hệ thống SHALL chỉ search trong quiz có permission

### Requirement 9: Backward Compatibility and Rollback

**User Story:** Là một developer, tôi muốn có khả năng rollback nếu migration gặp vấn đề, để đảm bảo hệ thống luôn hoạt động.

#### Acceptance Criteria

1. WHEN deploy new system THEN hệ thống SHALL maintain backward compatibility
2. WHEN migration fails THEN hệ thống SHALL có khả năng rollback to localStorage
3. WHEN rollback THEN hệ thống SHALL restore original localStorage data
4. WHEN switch between systems THEN hệ thống SHALL có feature flags
5. WHEN testing THEN hệ thống SHALL có staging environment với migrated data
6. IF production issues THEN hệ thống SHALL có emergency rollback procedure
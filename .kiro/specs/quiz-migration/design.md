# Design Document

## Overview

Thiết kế này mô tả kiến trúc chi tiết cho việc migrate hệ thống quiz từ localStorage sang database MySQL backend. Giải pháp được thiết kế để đảm bảo tính toàn vẹn dữ liệu, hi<PERSON><PERSON> suất cao, và khả năng mở rộng trong tương lai.

## Architecture

### High-Level Architecture

```
┌─────────────────┐    HTTP/REST API    ┌─────────────────┐    Prisma ORM    ┌─────────────────┐
│   Frontend      │ ◄─────────────────► │   NestJS        │ ◄──────────────► │   MySQL         │
│   (Next.js)     │                     │   Backend       │                  │   Database      │
│                 │                     │                 │                  │                 │
│ - Quiz Display  │                     │ - Quiz Module   │                  │ - Quiz Tables   │
│ - Admin Panel   │                     │ - Auth Module   │                  │ - User Tables   │
│ - Local Storage │                     │ - Validation    │                  │ - Audit Tables  │
│   (Legacy)      │                     │ - Caching       │                  │                 │
└─────────────────┘                     └─────────────────┘                  └─────────────────┘
```

### Database Architecture Decision

Sau khi phân tích cấu trúc dữ liệu phức tạp của MultiPartQuiz, tôi đề xuất **Hybrid Approach** kết hợp cả SQL normalization và JSON storage:

**Lý do chọn Hybrid Approach:**
1. **Flexibility**: JSON storage cho nested data phức tạp (questions, options, content)
2. **Performance**: SQL indexes cho queries thường dùng (title, type, created_at)
3. **Maintainability**: Dễ dàng thêm fields mới mà không cần migration
4. **Compatibility**: Giữ nguyên structure hiện tại của frontend

## Components and Interfaces

### 1. Database Schema

```prisma
model Quiz {
  id              String    @id @default(uuid())
  title           String
  testType        TestType
  totalTimeLimit  Int       // minutes
  isActive        Boolean   @default(true)
  
  // JSON storage for complex nested data
  content         Json      // Full quiz content (parts, questions, etc.)
  metadata        Json      // Additional metadata
  
  // Audit fields
  createdBy       String
  updatedBy       String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relations
  creator         User      @relation("QuizCreator", fields: [createdBy], references: [id])
  updater         User?     @relation("QuizUpdater", fields: [updatedBy], references: [id])
  sessions        QuizSession[]
  permissions     QuizPermission[]
  
  @@map("quizzes")
}

model QuizPermission {
  id          String    @id @default(uuid())
  quizId      String
  userId      String
  
  // Permission metadata
  assignedBy  String
  assignedAt  DateTime  @default(now())
  expiresAt   DateTime?
  isActive    Boolean   @default(true)
  
  // Relations
  quiz        Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user        User      @relation("UserQuizPermissions", fields: [userId], references: [id], onDelete: Cascade)
  assigner    User      @relation("QuizPermissionAssigner", fields: [assignedBy], references: [id])
  
  @@unique([quizId, userId])
  @@map("quiz_permissions")
}

model QuizSession {
  id              String    @id @default(uuid())
  quizId          String
  userId          String
  
  // Session data
  answers         Json      // User answers
  currentPart     Int       @default(1)
  timeRemaining   Int       // seconds
  isCompleted     Boolean   @default(false)
  isSubmitted     Boolean   @default(false)
  score           Float?
  
  // Timestamps
  startedAt       DateTime  @default(now())
  completedAt     DateTime?
  submittedAt     DateTime?
  
  // Relations
  quiz            Quiz      @relation(fields: [quizId], references: [id], onDelete: Cascade)
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([quizId, userId])
  @@map("quiz_sessions")
}

enum TestType {
  READING
  LISTENING
}
```

### 2. Backend API Design

#### Quiz Controller Endpoints

```typescript
@Controller('quizzes')
@UseGuards(AccessTokenGuard)
export class QuizController {
  
  // Public endpoints (authenticated users)
  @Get()
  async getQuizzes(@Query() query: GetQuizzesDto, @GetCurrentUser() user: User): Promise<QuizListResponse>
  
  @Get(':id')
  async getQuiz(@Param('id') id: string, @GetCurrentUser() user: User): Promise<QuizResponse>
  
  @Post(':id/sessions')
  async startQuizSession(@Param('id') id: string, @GetCurrentUser() user: User): Promise<QuizSessionResponse>
  
  @Put('sessions/:sessionId')
  async updateQuizSession(@Param('sessionId') sessionId: string, @Body() data: UpdateSessionDto): Promise<QuizSessionResponse>
  
  @Post('sessions/:sessionId/submit')
  async submitQuiz(@Param('sessionId') sessionId: string, @Body() answers: SubmitQuizDto): Promise<QuizResultResponse>
  
  // Admin endpoints
  @Post()
  @UseGuards(AdminRoleGuard)
  async createQuiz(@Body() createQuizDto: CreateQuizDto, @GetCurrentUser() user: User): Promise<QuizResponse>
  
  @Put(':id')
  @UseGuards(AdminRoleGuard)
  async updateQuiz(@Param('id') id: string, @Body() updateQuizDto: UpdateQuizDto, @GetCurrentUser() user: User): Promise<QuizResponse>
  
  @Delete(':id')
  @UseGuards(AdminRoleGuard)
  async deleteQuiz(@Param('id') id: string): Promise<void>
  
  @Post('import')
  @UseGuards(AdminRoleGuard)
  async importQuiz(@Body() importDto: ImportQuizDto, @GetCurrentUser() user: User): Promise<QuizResponse>
  
  @Get(':id/export')
  @UseGuards(AdminRoleGuard)
  async exportQuiz(@Param('id') id: string): Promise<ExportQuizResponse>
  
  // Quiz permission management (ADMIN only)
  @Post(':id/permissions')
  @UseGuards(AdminRoleGuard)
  async assignQuizToUsers(@Param('id') id: string, @Body() assignDto: AssignQuizDto, @GetCurrentUser() admin: User): Promise<void>
  
  @Delete(':id/permissions/:userId')
  @UseGuards(AdminRoleGuard)
  async revokeQuizAccess(@Param('id') id: string, @Param('userId') userId: string): Promise<void>
  
  @Get(':id/permissions')
  @UseGuards(AdminRoleGuard)
  async getQuizPermissions(@Param('id') id: string): Promise<QuizPermissionResponse[]>
}
```

#### DTOs and Validation

```typescript
// Input DTOs
export class CreateQuizDto {
  @IsString()
  @IsNotEmpty()
  title: string;

  @IsEnum(TestType)
  testType: TestType;

  @IsInt()
  @Min(1)
  @Max(180)
  totalTimeLimit: number;

  @IsObject()
  @ValidateNested()
  content: QuizContentDto;
}

export class QuizContentDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuizPartDto)
  parts: QuizPartDto[];

  @IsObject()
  metadata: QuizMetadataDto;
}

export class QuizPartDto {
  @IsInt()
  @Min(1)
  partNumber: number;

  @IsString()
  title: string;

  @IsObject()
  content: PartContentDto;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => QuestionDto)
  questions: QuestionDto[];
}

// Response DTOs
export class QuizResponse {
  id: string;
  title: string;
  testType: TestType;
  totalTimeLimit: number;
  content: any; // Full quiz content
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

export class QuizListResponse {
  quizzes: QuizSummaryDto[];
  total: number;
  page: number;
  limit: number;
}

export class AssignQuizDto {
  @IsArray()
  @IsString({ each: true })
  userIds: string[];

  @IsOptional()
  @IsDateString()
  expiresAt?: string;
}

export class QuizPermissionResponse {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  assignedBy: string;
  assignedAt: Date;
  expiresAt?: Date;
  isActive: boolean;
}
```

### 3. Service Layer Design

```typescript
@Injectable()
export class QuizService {
  constructor(
    private prisma: PrismaService,
    private cacheService: CacheService,
    private validationService: QuizValidationService
  ) {}

  async getQuizzes(query: GetQuizzesDto, userId: string): Promise<QuizListResponse> {
    // Implementation with user permission filtering, caching and pagination
  }

  async getQuizById(id: string, userId: string): Promise<Quiz | null> {
    // Implementation with permission check and caching
  }

  async checkQuizPermission(quizId: string, userId: string): Promise<boolean> {
    // Check if user has permission to access quiz
  }

  async createQuiz(data: CreateQuizDto, userId: string): Promise<Quiz> {
    // Validate and create quiz
  }

  async updateQuiz(id: string, data: UpdateQuizDto, userId: string): Promise<Quiz> {
    // Update with optimistic locking
  }

  async deleteQuiz(id: string): Promise<void> {
    // Soft delete with cascade handling
  }

  async importQuizFromLocalStorage(localStorageData: any[], userId: string): Promise<Quiz[]> {
    // Migration logic from localStorage format
  }

  async assignQuizToUsers(quizId: string, userIds: string[], assignedBy: string, expiresAt?: Date): Promise<void> {
    // Assign quiz permissions to multiple users
  }

  async revokeQuizAccess(quizId: string, userId: string): Promise<void> {
    // Revoke user access to quiz
  }

  async getQuizPermissions(quizId: string): Promise<QuizPermission[]> {
    // Get all users with access to quiz
  }
}

@Injectable()
export class QuizSessionService {
  async startSession(quizId: string, userId: string): Promise<QuizSession> {
    // Create new session or resume existing
  }

  async updateSession(sessionId: string, data: UpdateSessionDto): Promise<QuizSession> {
    // Update session state
  }

  async submitQuiz(sessionId: string, answers: any): Promise<QuizResult> {
    // Calculate score and complete session
  }
}
```

### 4. Frontend Integration Design

#### API Service Layer

```typescript
// services/quiz-api.service.ts
export class QuizApiService {
  private baseUrl = '/api/quizzes';

  async getQuizzes(params?: GetQuizzesParams): Promise<QuizListResponse> {
    const response = await axios.get(this.baseUrl, { params });
    return response.data;
  }

  async getQuiz(id: string): Promise<MultiPartQuiz> {
    const response = await axios.get(`${this.baseUrl}/${id}`);
    return this.transformToFrontendFormat(response.data);
  }

  async createQuiz(quiz: CreateQuizRequest): Promise<MultiPartQuiz> {
    const response = await axios.post(this.baseUrl, quiz);
    return this.transformToFrontendFormat(response.data);
  }

  async updateQuiz(id: string, quiz: UpdateQuizRequest): Promise<MultiPartQuiz> {
    const response = await axios.put(`${this.baseUrl}/${id}`, quiz);
    return this.transformToFrontendFormat(response.data);
  }

  async deleteQuiz(id: string): Promise<void> {
    await axios.delete(`${this.baseUrl}/${id}`);
  }

  private transformToFrontendFormat(backendQuiz: any): MultiPartQuiz {
    // Transform backend format to frontend MultiPartQuiz interface
    return {
      id: backendQuiz.id,
      title: backendQuiz.title,
      testType: backendQuiz.testType.toLowerCase(),
      totalTimeLimit: backendQuiz.totalTimeLimit,
      parts: backendQuiz.content.parts,
      metadata: backendQuiz.content.metadata
    };
  }
}
```

#### Migration Service

```typescript
// services/quiz-migration.service.ts
export class QuizMigrationService {
  constructor(private quizApiService: QuizApiService) {}

  async migrateFromLocalStorage(): Promise<MigrationResult> {
    try {
      // 1. Read all quiz data from localStorage
      const localQuizzes = this.readLocalStorageQuizzes();
      
      // 2. Validate and transform data
      const validatedQuizzes = this.validateQuizzes(localQuizzes);
      
      // 3. Upload to backend
      const migrationResults = await this.uploadQuizzes(validatedQuizzes);
      
      // 4. Verify migration
      await this.verifyMigration(migrationResults);
      
      // 5. Backup localStorage data
      this.backupLocalStorage();
      
      // 6. Clear localStorage (optional, with user confirmation)
      // this.clearLocalStorage();
      
      return {
        success: true,
        migratedCount: migrationResults.length,
        errors: []
      };
    } catch (error) {
      return {
        success: false,
        migratedCount: 0,
        errors: [error.message]
      };
    }
  }

  private readLocalStorageQuizzes(): any[] {
    // Read from localStorage using existing functions
    return getAllQuizzes();
  }

  private async uploadQuizzes(quizzes: any[]): Promise<string[]> {
    const results = [];
    for (const quiz of quizzes) {
      try {
        const created = await this.quizApiService.createQuiz(quiz);
        results.push(created.id);
      } catch (error) {
        console.error(`Failed to migrate quiz ${quiz.title}:`, error);
        throw error;
      }
    }
    return results;
  }
}
```

## Data Models

### Frontend-Backend Data Mapping

```typescript
// Frontend MultiPartQuiz → Backend Quiz mapping
const frontendToBackend = (frontendQuiz: MultiPartQuiz) => ({
  title: frontendQuiz.title,
  testType: frontendQuiz.testType.toUpperCase(),
  totalTimeLimit: frontendQuiz.totalTimeLimit,
  content: {
    parts: frontendQuiz.parts,
    metadata: frontendQuiz.metadata
  }
});

// Backend Quiz → Frontend MultiPartQuiz mapping  
const backendToFrontend = (backendQuiz: any): MultiPartQuiz => ({
  id: backendQuiz.id,
  title: backendQuiz.title,
  testType: backendQuiz.testType.toLowerCase(),
  totalTimeLimit: backendQuiz.totalTimeLimit,
  parts: backendQuiz.content.parts,
  metadata: backendQuiz.content.metadata
});
```

## Error Handling

### Backend Error Handling

```typescript
@Catch()
export class QuizExceptionFilter implements ExceptionFilter {
  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    
    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let message = 'Internal server error';
    
    if (exception instanceof QuizNotFoundException) {
      status = HttpStatus.NOT_FOUND;
      message = 'Quiz not found';
    } else if (exception instanceof QuizValidationException) {
      status = HttpStatus.BAD_REQUEST;
      message = exception.message;
    } else if (exception instanceof UnauthorizedException) {
      status = HttpStatus.UNAUTHORIZED;
      message = 'Unauthorized access';
    }
    
    response.status(status).json({
      statusCode: status,
      message,
      timestamp: new Date().toISOString()
    });
  }
}
```

### Frontend Error Handling

```typescript
// hooks/useQuizApi.ts
export const useQuizApi = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleApiCall = async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);
      return await apiCall();
    } catch (err) {
      const errorMessage = err.response?.data?.message || 'An error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { loading, error, handleApiCall };
};
```

## Testing Strategy

### Backend Testing

1. **Unit Tests**: Service layer logic, validation, transformations
2. **Integration Tests**: Database operations, API endpoints
3. **E2E Tests**: Complete quiz workflows
4. **Migration Tests**: localStorage to database migration scenarios

### Frontend Testing

1. **Component Tests**: Quiz display components, admin panels
2. **Integration Tests**: API service integration
3. **Migration Tests**: localStorage migration scenarios
4. **E2E Tests**: Complete user workflows

### Test Data Strategy

```typescript
// Test data factory for consistent testing
export class QuizTestDataFactory {
  static createReadingQuiz(): MultiPartQuiz {
    return {
      id: 'test-reading-1',
      title: 'Test Reading Quiz',
      testType: 'reading',
      totalTimeLimit: 60,
      parts: [
        {
          partNumber: 1,
          title: 'Part 1',
          content: {
            title: 'Reading Passage 1',
            paragraphs: [
              { label: 'A', text: 'Test paragraph A content...' }
            ]
          },
          questions: [
            {
              id: 'p1q1',
              type: 'MULTIPLE_CHOICE',
              prompt: 'Test question?',
              options: [
                { id: 'a', text: 'Option A' },
                { id: 'b', text: 'Option B' }
              ]
            }
          ]
        }
      ],
      metadata: { totalQuestions: 1 }
    };
  }
}
```

## Performance Considerations

### Caching Strategy

1. **Redis Caching**: Cache frequently accessed quizzes
2. **Database Indexing**: Optimize queries with proper indexes
3. **Pagination**: Implement efficient pagination for quiz lists
4. **Lazy Loading**: Load quiz parts on demand for large quizzes

### Database Optimization

```sql
-- Recommended indexes
CREATE INDEX idx_quizzes_test_type ON quizzes(testType);
CREATE INDEX idx_quizzes_created_at ON quizzes(createdAt);
CREATE INDEX idx_quizzes_active ON quizzes(isActive);
CREATE INDEX idx_quiz_sessions_user_quiz ON quiz_sessions(userId, quizId);
```

## Security Considerations

1. **Authentication**: JWT-based authentication for all endpoints
2. **Authorization**: Role-based access control (ADMIN vs USER)
3. **Input Validation**: Comprehensive validation using class-validator
4. **Rate Limiting**: Prevent abuse of API endpoints
5. **Data Sanitization**: Sanitize user inputs to prevent XSS
6. **Audit Logging**: Track all quiz modifications for compliance
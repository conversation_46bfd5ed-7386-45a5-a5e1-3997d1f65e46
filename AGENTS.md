# Repository Guidelines

## Rules
This file provides guidance to <PERSON> when working with code in this repository.
  - Keep source code and files under 500 lines.
  - If the being generated file is too big, generate small pieces.
  - Do not refactor code unless necessary. If you need to refactor, please do it in a separate task, only refactor if I ask you to do so.
  - If you want to: Explore and Learn Codebase, Work with Symbols, Knowledge Management, Workflow. Prioritize using mcp tool serena first
  - Run mcp tool ctx-compress to compress context if the following requirements are met:
    - token is too long, need to be reduced to save context
    - if the document is not related to the codebase
    - after calling other mcp tools, tools, the output is too long, need to be compressed (do not compress the codebase)
    note: absolutely do not compress anything related to code, codebase
  - when you work with nextjs project always use playwright mcp to test before complete task, i alway run fe server at port 3000 and be server at port 8228, so you can not re-run server

## Authentication Information
- User: <EMAIL> / password123
- Admin: admin / admin123
- Backend Port: 8228
- Frontend Port: 3000

## Project Structure & Module Organization
The repo is split into `fe-ielts-exams/` (Next.js 15 frontend) and `be-ielts-exams/` (NestJS 10 API). Frontend features live in `src/app`, shared UI in `src/components`, and network helpers in `src/services`; Playwright specs live under `tests/`. The backend groups domain logic per module in `src/modules`, with shared config in `src/config`, middleware and guards in `src/middleware`, Prisma schema in `prisma/`, and RSA keys generated into `keys/` (do not commit).

## Build, Test & Development Commands
I alway run fe server at port 3000 and be server at port 8228, so you can not re-run server
Frontend: `npm run dev` starts the Turbopack dev server, `npm run build` compiles for production, and `npm run lint` applies Next.js + ESLint rules. Backend: `yarn start:dev` runs the Nest watcher, `yarn build` emits the compiled `dist/`, and `yarn lint` fixes ESLint + Prettier issues. Run Playwright from the frontend root with `npx playwright test`; backend unit suites use `yarn test`, and end-to-end API checks run via `yarn test:e2e` after provisioning the database.

## Coding Style & Naming Conventions
Use TypeScript throughout with 2-space indentation and keep files under 500 lines when possible. Components, hooks, and context providers follow PascalCase (`ResultsSummaryCard.tsx`), utility modules use camelCase (`formatScore.ts`), and environment examples live in `.env.example`. Frontend linting relies on `next/core-web-vitals`; backend rules extend `@typescript-eslint/recommended` with Prettier—run the provided lint scripts before committing to avoid CI failures.

## Testing Guidelines
I alway run fe server at port 3000 and be server at port 8228, so you can not re-run server
Frontend interactions should include Playwright IDs prefixed with `data-testid="ielts-"` to keep selectors stable. Backend tests live in `test/` with names mirroring the subject file plus `.spec.ts`; aim to cover new endpoints with unit specs and, when touching Prisma flows, update the `test/jest-e2e.json` suite. Ensure your changes keep `npx playwright test` and `yarn test --coverage` green before opening a PR, and capture regression cases in fixtures when fixing bugs.

## Commit & Pull Request Guidelines
Follow conventional commits (`feat:`, `fix:`, `chore:`) and keep subject lines under 72 characters. Each PR should describe the user-facing impact, list key commands run (tests, migrations), and link tracking issues or Jira tickets. Include screenshots or recordings for UI updates, attach sample API responses for backend adjustments, and note any configuration changes (env vars, Prisma migrations). Request at least one reviewer and wait for automated checks to pass before merging.

## Security & Configuration Tips
Generate JWT key pairs once per developer machine with `./be-ielts-exams/generate-keys.sh`, and never commit the output in `be-ielts-exams/keys/`. Copy `.env.example` files into `.env.local` or `.env` and rotate secrets using `openssl rand -base64 32`. When adding new secrets or Prisma migrations, document them in the PR body and update `ENVIRONMENT_VARIABLES.md` as needed.

## Note 
I alway run fe server at port 3000 and be server at port 8228, so you can not re-run server.
#!/usr/bin/env node

/**
 * Script tạo tài khoản admin mới cho IELTS Exams
 * Usage: node create-admin.js
 */

const { PrismaClient } = require('@prisma/client');
const argon = require('argon2');

// Thông tin admin mới
const ADMIN_DATA = {
  username: 'ieltsadmin',
  email: '<EMAIL>',  // Email mặc định
  name: 'IELTS Administrator',
  password: 'ielts10cham',
  phoneNumber: null,
  role: 'ADMIN'
};

async function createAdmin() {
  const prisma = new PrismaClient();

  try {
    console.log('🚀 Đang tạo tài khoản admin mới...');

    // Kiểm tra username đã tồn tại chưa
    const existingUser = await prisma.user.findFirst({
      where: {
        username: ADMIN_DATA.username
      }
    });

    if (existingUser) {
      console.log('❌ Username "ieltsadmin" đã tồn tại!');
      console.log('Thông tin user hiện tại:');
      console.log(`- ID: ${existingUser.id}`);
      console.log(`- Username: ${existingUser.username}`);
      console.log(`- Email: ${existingUser.email}`);
      console.log(`- Role: ${existingUser.role}`);
      console.log(`- Active: ${existingUser.active}`);

      // Hỏi có muốn cập nhật mật khẩu không
      const answer = process.argv.includes('--update-password');
      if (answer) {
        console.log('🔄 Đang cập nhật mật khẩu...');
        const hashedPassword = await argon.hash(ADMIN_DATA.password);

        await prisma.user.update({
          where: { id: existingUser.id },
          data: {
            password: hashedPassword,
            active: true  // Đảm bảo account được kích hoạt
          }
        });

        console.log('✅ Đã cập nhật mật khẩu cho user "ieltsadmin"');
        console.log(`👤 Username: ${ADMIN_DATA.username}`);
        console.log(`🔑 Password: ${ADMIN_DATA.password}`);
      } else {
        console.log('💡 Để cập nhật mật khẩu, chạy: node create-admin.js --update-password');
      }

      return;
    }

    // Kiểm tra email đã tồn tại chưa
    const existingEmail = await prisma.user.findFirst({
      where: {
        email: ADMIN_DATA.email
      }
    });

    if (existingEmail) {
      console.log('⚠️  Email đã tồn tại, sẽ sử dụng email khác...');
      ADMIN_DATA.email = `ieltsadmin+${Date.now()}@admin.com`;
    }

    // Hash password
    const hashedPassword = await argon.hash(ADMIN_DATA.password);

    // Tạo user mới
    const newAdmin = await prisma.user.create({
      data: {
        username: ADMIN_DATA.username,
        email: ADMIN_DATA.email,
        name: ADMIN_DATA.name,
        password: hashedPassword,
        phoneNumber: ADMIN_DATA.phoneNumber,
        role: ADMIN_DATA.role,
        active: true
      }
    });

    console.log('✅ Tạo tài khoản admin thành công!');
    console.log('📋 Thông tin tài khoản:');
    console.log(`- ID: ${newAdmin.id}`);
    console.log(`- Username: ${newAdmin.username}`);
    console.log(`- Email: ${newAdmin.email}`);
    console.log(`- Name: ${newAdmin.name}`);
    console.log(`- Role: ${newAdmin.role}`);
    console.log(`- Active: ${newAdmin.active}`);
    console.log('');
    console.log('🔐 Thông tin đăng nhập:');
    console.log(`👤 Username: ${ADMIN_DATA.username}`);
    console.log(`🔑 Password: ${ADMIN_DATA.password}`);
    console.log('');
    console.log('🌐 Bạn có thể đăng nhập tại:');
    console.log('- Frontend: http://localhost:3000/login');
    console.log('- Production: http://your-domain:3000/login');

  } catch (error) {
    console.error('❌ Lỗi khi tạo admin:', error.message);

    if (error.code === 'P2002') {
      console.log('💡 Lỗi: Username hoặc email đã tồn tại');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Chạy script
if (require.main === module) {
  createAdmin().catch(console.error);
}

module.exports = { createAdmin, ADMIN_DATA };
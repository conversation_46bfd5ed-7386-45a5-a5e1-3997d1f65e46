# CLAUDE.md
This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.
  - Keep source code and files under 500 lines.
  - You can save document as English, but Always respond by Vietnamese in chat.
  - If the being generated file is too big, generate small pieces.
  - Do not refactor code unless necessary. If you need to refactor, please do it in a separate task, only refactor if I ask you to do so.
  - If unsure, use tools like perplexity, deepwiki and memory-ai to learn about it, after learning if still unsure admit it and let me know
  - If you want to: Explore and Learn Codebse, Work with Symbols, Knowledge Management, Workflow. Prioritize using mcp tool serena first
  - Run mcp tool ctx-compress to compress context if the following requirements are met:
    - token is too long, need to be reduced to save context
    - if the document is not related to the codebase
    - after calling other mcp tools, tools, the output is too long, need to be compressed (do not compress the codebase)
    note: absolutely do not compress anything related to code, codebase
  - when you work with nextjs project always use playwright mcp to test before complete task, i alway run server at port 3000, so you can not re-run server
- The backend and frontend servers are always running, so you never need to start them.
**Thông tin xác thực:**
- User: ng<PERSON><PERSON><PERSON>@example.com / password123
- Admin: admin / admin123
- Backend Port: 8228
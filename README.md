# 🎯 IELTS Exams Application - VPS Deployment Guide

## 📋 Tổng quan

Ứng dụng IELTS Exams với 2 môi trường deploy riêng biệt trên VPS:
- **Production Environment**: Môi trường sản xuất chính thức
- **Development Environment**: Môi trường test và phát triển

## 🏗️ Kiến trúc hệ thống

- **Frontend**: Next.js (Port: 3000/3001)
- **Backend**: NestJS (Port: 8228/8229)
- **Database**: MySQL 8.0
- **Container**: Docker + Docker Compose

## ⚙️ Chuẩn bị VPS

### Yêu cầu VPS
- **OS**: Ubuntu 20.04+
- **RAM**: Tối thiểu 4GB (Khuyến nghị 8GB+)
- **Storage**: Tối thiểu 20GB
- **CPU**: 2+ cores

### Phần mềm đã cài sẵn
- ✅ Docker Engine
- ✅ Docker Compose
- ✅ npm

## 🚀 Deploy Production Environment

### 1. Chuẩn bị file cấu hình

```bash
# Clone repository
git clone <your-repository-url>
cd ielts-exams

# Copy file cấu hình production
cp .env.production .env
```

### 2. Cấu hình biến môi trường

Chỉnh sửa file `.env` với thông tin VPS thật:

```bash
nano .env
```

**Các biến quan trọng cần thay đổi:**

```bash
# DOMAIN/IP thật của VPS
SERVER_DOMAIN=your-production-domain.com
# hoặc: SERVER_DOMAIN=123.456.789.123

# MẬT KHẨU MẠNH CHO DATABASE
MYSQL_ROOT_PASSWORD=YourStrongRootPassword123!
MYSQL_PASSWORD=YourStrongDBPassword123!

# JWT SECRETS MẠNH
JWT_SECRET=your-super-strong-jwt-secret-here
JWT_ACCESS_SECRET=your-strong-access-secret-here
JWT_REFRESH_SECRET=your-strong-refresh-secret-here
JWT_PASSWORD_RESET_SECRET=your-strong-reset-secret-here
```

### 3. Tạo thư mục cần thiết

```bash
mkdir -p ./be-ielts-exams/uploads
chmod 755 ./be-ielts-exams/uploads
```

### 4. Deploy Production

```bash
# Dừng tất cả container cũ
docker-compose -f docker-compose.yml -f docker-compose.production.yml down

# Xóa volume cũ nếu cần (CHÚ Ý: sẽ mất dữ liệu)
# docker volume rm ielts-exams-prod_mysql_data

# Build và chạy production
docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.production.yml up -d --build
```

### 5. Kiểm tra Production

```bash
# Xem trạng thái containers
docker ps

# Xem logs
docker logs ielts-backend-prod
docker logs ielts-frontend-prod
docker logs mysql-ielts-prod

# Test health endpoints
curl http://your-domain:8228/health
curl http://your-domain:3000
```

### 6. Cấu hình Firewall (nếu cần)

```bash
# Cho phép các port cần thiết
sudo ufw allow 3000    # Frontend
sudo ufw allow 8228    # Backend
sudo ufw allow 22      # SSH

# Kích hoạt firewall
sudo ufw enable
```

## 🔧 Deploy Development Environment

### 1. Chuẩn bị file cấu hình

```bash
# Copy file cấu hình development
cp .env.development .env
```

### 2. Cấu hình biến môi trường

```bash
nano .env
```

**Thay đổi SERVER_DOMAIN:**

```bash
SERVER_DOMAIN=your-dev-domain.com
# hoặc: SERVER_DOMAIN=123.456.789.123
```

### 3. Deploy Development

```bash
# Dừng containers cũ
docker-compose -f docker-compose.yml -f docker-compose.development.yml down

# Build và chạy development
docker-compose --env-file .env.development -f docker-compose.yml -f docker-compose.development.yml up -d --build
```

### 4. Kiểm tra Development

```bash
# Xem trạng thái
docker ps

# Test endpoints
curl http://your-domain:8229/health    # Backend dev
curl http://your-domain:3001           # Frontend dev
curl http://your-domain:8081           # PhpMyAdmin
```

## 📊 Truy cập ứng dụng

### Production Environment
- **Frontend**: `http://your-domain.com:3000`
- **Backend API**: `http://your-domain.com:8228`
- **Health Check**: `http://your-domain.com:8228/health`

### Development Environment
- **Frontend**: `http://your-domain.com:3001`
- **Backend API**: `http://your-domain.com:8229`
- **Health Check**: `http://your-domain.com:8229/health`
- **PhpMyAdmin**: `http://your-domain.com:8081`

## 🔒 Tài khoản mặc định

```
User: <EMAIL> / password123
Admin: admin / admin123
```

## 🛠️ Quản lý và bảo trì

### Xem logs

```bash
# Production
docker logs ielts-backend-prod -f
docker logs ielts-frontend-prod -f

# Development
docker logs ielts-backend-dev -f
docker logs ielts-frontend-dev -f
```

### Backup Database

```bash
# Production
docker exec mysql-ielts-prod mysqldump -u root -p ielts_prod_db > backup_prod_$(date +%Y%m%d_%H%M%S).sql

# Development
docker exec mysql-ielts-dev mysqldump -u root -p ielts_dev_db > backup_dev_$(date +%Y%m%d_%H%M%S).sql
```

### Restore Database

```bash
# Production
docker exec -i mysql-ielts-prod mysql -u root -p ielts_prod_db < backup_file.sql

# Development
docker exec -i mysql-ielts-dev mysql -u root -p ielts_dev_db < backup_file.sql
```

### Update ứng dụng

```bash
# Pull code mới
git pull origin main

# Rebuild và restart
# Production
docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.production.yml down
docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.production.yml up -d --build

# Development
docker-compose --env-file .env.development -f docker-compose.yml -f docker-compose.development.yml down
docker-compose --env-file .env.development -f docker-compose.yml -f docker-compose.development.yml up -d --build
```

### Dọn dẹp system

```bash
# Xóa images cũ
docker image prune -a

# Xóa volumes không dùng
docker volume prune

# Xóa networks không dùng
docker network prune
```

## ❗ Troubleshooting

### 1. Port đã được sử dụng

```bash
# Kiểm tra port
sudo netstat -tulpn | grep :3000
sudo netstat -tulpn | grep :8228

# Kill process
sudo kill -9 <PID>
```

### 2. Container không start

```bash
# Xem logs chi tiết
docker logs <container_name>

# Kiểm tra tài nguyên
docker stats
free -h
df -h
```

### 3. Database connection failed

```bash
# Kiểm tra MySQL logs
docker logs mysql-ielts-prod
docker logs mysql-ielts-dev

# Kiểm tra network
docker network ls
docker network inspect ielts-exams-prod_ielts-network
```

### 4. Memory issues

```bash
# Kiểm tra memory
free -h
docker stats

# Thêm swap nếu cần
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

## 🔧 Commands Quick Reference

```bash
# Start Production
docker-compose --env-file .env.production -f docker-compose.yml -f docker-compose.production.yml up -d

# Start Development
docker-compose --env-file .env.development -f docker-compose.yml -f docker-compose.development.yml up -d

# Stop Production
docker-compose -f docker-compose.yml -f docker-compose.production.yml down

# Stop Development
docker-compose -f docker-compose.yml -f docker-compose.development.yml down

# View all containers
docker ps -a

# View logs
docker logs <container_name> -f
```

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra logs: `docker logs <container_name>`
2. Kiểm tra biến môi trường
3. Kiểm tra firewall và ports
4. Kiểm tra tài nguyên VPS (RAM, disk)
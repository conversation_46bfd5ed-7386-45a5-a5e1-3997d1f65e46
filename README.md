# IELTS Exams Platform

Full-stack IELTS examination platform with Next.js frontend and NestJS backend.

## 🚀 Quick Start

```bash
# 1. Frontend setup
cd fe-ielts-exams
npm install
cp .env.example .env.local

# 2. Backend setup
cd ../be-ielts-exams
yarn install
./generate-keys.sh  # Generate JWT keys (REQUIRED)
cp .env.example .env # Edit .env with your database config
npx prisma generate
npx prisma db push

# 3. Start development servers
# Terminal 1 - Backend (port 8228)
cd be-ielts-exams
yarn start:dev

# Terminal 2 - Frontend (port 3000)  
cd fe-ielts-exams
npm run dev
```

## 📂 Project Structure

```
ielts-exams/
├── fe-ielts-exams/     # Next.js 15 frontend
│   ├── src/
│   │   ├── app/        # App Router pages
│   │   ├── components/ # UI components
│   │   ├── contexts/   # React contexts
│   │   └── services/   # API services
│   └── tests/          # Playwright E2E tests
│
└── be-ielts-exams/     # NestJS backend API
    ├── src/
    │   ├── modules/    # Feature modules
    │   ├── config/     # Configuration
    │   └── middleware/ # Security middleware
    ├── keys/           # JWT RSA keys (generated)
    └── prisma/         # Database schema
```

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router & React 19
- **Styling**: TailwindCSS 4 + Radix UI components  
- **Forms**: React Hook Form + Zod validation
- **HTTP**: Axios with interceptors
- **Testing**: Playwright E2E tests

### Backend  
- **Framework**: NestJS 10 with TypeScript
- **Database**: Prisma ORM + MySQL
- **Authentication**: JWT (RSA signed) + httpOnly cookies
- **Security**: Helmet, CORS, rate limiting, input sanitization
- **Testing**: Jest unit & E2E tests

## 🔐 Security Features

- **httpOnly Cookies**: Tokens stored securely, not accessible to JavaScript
- **RSA JWT Signing**: Private/public key pair for token verification  
- **CORS Protection**: Specific origins allowed for credentials
- **Rate Limiting**: Protect against brute force attacks
- **Input Sanitization**: Prevent XSS and injection attacks
- **Helmet Security**: HTTP security headers

## 🧪 Testing

```bash
# Frontend E2E tests
cd fe-ielts-exams
npx playwright test

# Backend tests  
cd be-ielts-exams
yarn test          # Unit tests
yarn test:e2e      # E2E tests
```

## 🚨 Important Security Notes

⚠️ **JWT Keys**: After cloning, run `./generate-keys.sh` in `be-ielts-exams/` to generate RSA keys. **Never commit these keys to version control.**

🔒 **Environment Variables**: Copy `.env.example` files and update with secure secrets:
```bash
# Generate random JWT secrets
openssl rand -base64 32  # Use for JWT_ACCESS_SECRET
openssl rand -base64 32  # Use for JWT_REFRESH_SECRET  
openssl rand -base64 32  # Use for JWT_PASSWORD_RESET_SECRET
```

## 📚 Documentation

- **[WARP.md](./WARP.md)** - Comprehensive development guide
- **[Frontend README](./fe-ielts-exams/README.md)** - Next.js specific docs
- **[Backend README](./be-ielts-exams/README.md)** - NestJS API documentation

## 🐛 Troubleshooting

**Backend won't start:**
```bash
cd be-ielts-exams
# Check if JWT keys exist
ls -la keys/
# If missing, generate them
./generate-keys.sh
```

**CORS errors:**
- Ensure backend `FRONTEND_URL=http://localhost:3000` in `.env`
- Start backend before frontend

**Database issues:**
```bash  
cd be-ielts-exams
npx prisma generate
npx prisma db push
```

## 📄 License

This project is licensed under the UNLICENSED License.